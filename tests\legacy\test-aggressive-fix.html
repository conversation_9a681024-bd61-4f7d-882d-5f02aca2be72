<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激进清理修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .device-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .device-id {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 10px;
            min-width: 200px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(46, 204, 113, 0.3);
            border: 1px solid #2ecc71;
        }
        
        .status.error {
            background: rgba(231, 76, 60, 0.3);
            border: 1px solid #e74c3c;
        }
        
        .status.info {
            background: rgba(52, 152, 219, 0.3);
            border: 1px solid #3498db;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 激进清理修复测试</h1>
        
        <div class="device-info">
            <h3>📱 当前设备信息</h3>
            <div>
                <strong>设备ID：</strong>
                <div class="device-id" id="current-device-id">加载中...</div>
            </div>
            <div>
                <strong>缓存状态：</strong>
                <span id="cache-status">检查中...</span>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="loadDeviceInfo()">🔄 刷新设备信息</button>
            <button class="btn" onclick="testAggressiveCleanup()" id="cleanup-btn">🔥 测试激进清理</button>
        </div>
        
        <div id="status-container"></div>
        
        <div id="comparison-container" style="display: none;">
            <h3>📊 清理前后对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>清理前</h4>
                    <div class="device-id" id="before-device-id"></div>
                </div>
                <div class="comparison-item">
                    <h4>清理后</h4>
                    <div class="device-id" id="after-device-id"></div>
                </div>
            </div>
            <div id="change-analysis"></div>
        </div>
    </div>

    <script>
        let originalDeviceId = null;
        
        // 加载设备信息
        async function loadDeviceInfo() {
            try {
                showStatus('正在加载设备信息...', 'info');
                
                // 模拟API调用（实际应用中会调用后端API）
                const response = await fetch('/api/device-info');
                const deviceInfo = await response.json();
                
                document.getElementById('current-device-id').textContent = deviceInfo.deviceId;
                document.getElementById('cache-status').textContent = deviceInfo.hasCachedId ? '已缓存' : '无缓存';
                
                showStatus('设备信息加载完成', 'success');
            } catch (error) {
                document.getElementById('current-device-id').textContent = '加载失败';
                document.getElementById('cache-status').textContent = '未知';
                showStatus('加载设备信息失败: ' + error.message, 'error');
            }
        }
        
        // 测试激进清理
        async function testAggressiveCleanup() {
            const cleanupBtn = document.getElementById('cleanup-btn');
            cleanupBtn.disabled = true;
            cleanupBtn.innerHTML = '<span class="loading"></span> 执行激进清理...';
            
            try {
                // 1. 备份当前设备ID
                const currentDeviceId = document.getElementById('current-device-id').textContent;
                originalDeviceId = currentDeviceId;
                
                showStatus('🔄 开始激进清理测试...', 'info');
                showStatus('📋 备份当前设备ID: ' + currentDeviceId.substring(0, 16) + '...', 'info');
                
                // 2. 执行激进清理
                showStatus('🔥 执行激进清理操作...', 'info');
                
                const cleanupResponse = await fetch('/api/aggressive-cleanup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        preserveActivation: true,
                        aggressiveMode: true,
                        multiRoundClean: true,
                        extendedMonitoring: true
                    })
                });
                
                const cleanupResult = await cleanupResponse.json();
                
                if (cleanupResult.success) {
                    showStatus('✅ 激进清理执行完成', 'success');
                    
                    // 3. 检查清理效果
                    setTimeout(async () => {
                        await checkCleanupEffect();
                    }, 2000);
                } else {
                    showStatus('❌ 激进清理失败: ' + cleanupResult.error, 'error');
                }
                
            } catch (error) {
                showStatus('❌ 测试过程中发生错误: ' + error.message, 'error');
            } finally {
                cleanupBtn.disabled = false;
                cleanupBtn.innerHTML = '🔥 测试激进清理';
            }
        }
        
        // 检查清理效果
        async function checkCleanupEffect() {
            try {
                showStatus('🔍 检查清理效果...', 'info');
                
                const response = await fetch('/api/device-info');
                const deviceInfo = await response.json();
                const newDeviceId = deviceInfo.deviceId;
                
                // 显示对比结果
                document.getElementById('before-device-id').textContent = originalDeviceId;
                document.getElementById('after-device-id').textContent = newDeviceId;
                document.getElementById('comparison-container').style.display = 'block';
                
                // 分析变化
                if (originalDeviceId !== newDeviceId) {
                    const similarity = calculateSimilarity(originalDeviceId, newDeviceId);
                    const similarityPercent = (similarity * 100).toFixed(2);
                    
                    let changeLevel = '';
                    if (similarity < 0.1) {
                        changeLevel = '<span class="highlight">优秀 (完全不同)</span>';
                    } else if (similarity < 0.3) {
                        changeLevel = '<span class="highlight">良好 (大部分不同)</span>';
                    } else {
                        changeLevel = '<span class="highlight">不足 (相似度过高)</span>';
                    }
                    
                    document.getElementById('change-analysis').innerHTML = `
                        <div class="status success">
                            🎉 <strong>激进清理修复成功！</strong><br>
                            设备ID已成功变化<br>
                            相似度: ${similarityPercent}% (越低越好)<br>
                            变化程度: ${changeLevel}
                        </div>
                    `;
                    
                    showStatus('🎉 设备ID变化验证成功！', 'success');
                } else {
                    document.getElementById('change-analysis').innerHTML = `
                        <div class="status error">
                            ❌ <strong>设备ID未发生变化</strong><br>
                            激进清理模式可能仍有问题
                        </div>
                    `;
                    
                    showStatus('❌ 设备ID未发生变化，需要进一步调试', 'error');
                }
                
                // 更新当前显示的设备ID
                document.getElementById('current-device-id').textContent = newDeviceId;
                
            } catch (error) {
                showStatus('❌ 检查清理效果失败: ' + error.message, 'error');
            }
        }
        
        // 显示状态信息
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            container.appendChild(statusDiv);
            
            // 自动滚动到底部
            statusDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 计算字符串相似度
        function calculateSimilarity(str1, str2) {
            if (str1 === str2) return 1;
            if (str1.length === 0 || str2.length === 0) return 0;
            
            let matches = 0;
            const minLength = Math.min(str1.length, str2.length);
            
            for (let i = 0; i < minLength; i++) {
                if (str1[i] === str2[i]) {
                    matches++;
                }
            }
            
            return matches / Math.max(str1.length, str2.length);
        }
        
        // 页面加载时自动加载设备信息
        window.addEventListener('load', loadDeviceInfo);
    </script>
</body>
</html>
