# 🚀 构建和发布指南

## 🏗️ 快速构建

```bash
npm run build:status          # 📊 检查配置状态
npm run build:release         # 🚀 构建应用
npm run build:release:publish # 🚀 构建并发布到GitHub
npm run build:force           # 🔥 强制构建（忽略Git状态）
```

## 🌐 部署流程

```bash
npm run server:start          # 🚀 启动ngrok服务
npm run full-deploy           # 🌐 完整部署流程
```

## 📦 构建产物

构建完成后，产物位于：

```
modules/desktop-client/dist-final/
├── Augment设备管理器 Setup 1.0.0.exe    # Windows安装包
├── latest.yml                           # 自动更新配置
└── win-unpacked/                        # 解压版本
```

## 🔄 自动配置机制

### 配置同步流程

1. **服务器端**: ngrok 启动时自动推送配置到 GitHub
2. **客户端**: 从 GitHub 获取最新配置 + 本地自动发现
3. **打包时**: 构建前自动获取最新配置

### 配置文件位置

- **GitHub 远程**: `server-config.json`
- **客户端**: `modules/desktop-client/src/config.js`
- **本地**: `modules/desktop-client/public/server-config.json`

## 📊 完整工作流程

### 开发阶段

```bash
npm run dev                   # 启动开发环境
npm run build:status          # 检查配置
```

### 发布阶段

```bash
git add . && git commit -m "更新"
npm run build:release:publish # 构建并发布
```

### 部署阶段

```bash
npm run server:start          # 启动ngrok服务
```

## 🔍 故障排除

| 问题       | 解决方案                                               |
| ---------- | ------------------------------------------------------ |
| 配置不一致 | `npm run build:status` → `npm run build:update-config` |
| 构建失败   | `npm run build:force`                                  |
| 发布失败   | 检查 GitHub Token 和仓库权限                           |

## ⚠️ 注意事项

1. **GitHub 配置**: 确保 package.json 中发布配置正确
2. **网络要求**: 能访问 GitHub API 和 CDN
3. **环境变量**: 可选设置`GITHUB_TOKEN`
