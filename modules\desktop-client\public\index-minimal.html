<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment设备管理器</title>
    <script src="tailwind.css"></script>
    <style>
      /* 极简风格样式 */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(8px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(-12px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.4s ease-out;
      }
      .animate-slideIn {
        animation: slideIn 0.3s ease-out;
      }
      .animate-pulse {
        animation: pulse 2s infinite;
      }
      .animate-spin {
        animation: spin 1s linear infinite;
      }

      /* 卡片样式 */
      .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
      }

      .card:hover {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      }

      /* 标签页 */
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease-out;
      }

      /* 极简背景 */
      .minimal-bg {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      }

      /* 状态指示器 */
      .status-dot {
        position: relative;
      }

      .status-dot::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: inherit;
        animation: pulse 2s infinite;
        opacity: 0.4;
      }

      /* 滚动条 */
      ::-webkit-scrollbar {
        width: 4px;
      }
      ::-webkit-scrollbar-track {
        background: #f1f5f9;
      }
      ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      /* 加载状态 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(248, 250, 252, 0.9);
        backdrop-filter: blur(4px);
        z-index: 9999;
      }
    </style>
  </head>
  <body class="min-h-screen minimal-bg">
    <!-- 加载状态 -->
    <div
      id="loading"
      class="loading-overlay hidden items-center justify-center"
    >
      <div class="text-center">
        <div
          class="w-8 h-8 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin mx-auto mb-3"
        ></div>
        <p class="text-slate-600 text-sm">加载中...</p>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="container mx-auto px-6 py-8 max-w-6xl">
      <!-- 头部 -->
      <header class="text-center mb-12 animate-fadeIn">
        <div class="flex items-center justify-center gap-3 mb-4">
          <div
            class="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-2xl flex items-center justify-center"
          >
            <svg
              class="w-6 h-6 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
            </svg>
          </div>
          <h1 class="text-3xl font-light text-slate-800">Augment 设备管理器</h1>
        </div>
        <p class="text-slate-500 text-lg font-light">
          Cursor IDE 设备限制解决方案
        </p>

        <!-- 状态指示器 -->
        <div
          class="inline-flex items-center gap-2 mt-6 px-4 py-2 bg-white/80 rounded-full shadow-sm"
        >
          <div
            id="quick-status-indicator"
            class="w-2 h-2 bg-red-500 rounded-full status-dot"
          ></div>
          <span id="quick-status-text" class="text-sm text-slate-600"
            >设备未激活</span
          >
        </div>
      </header>

      <!-- 导航标签 -->
      <nav class="flex justify-center mb-8 animate-slideIn">
        <div class="flex bg-white/80 rounded-2xl p-1 shadow-sm">
          <button
            id="tab-btn-dashboard"
            onclick="switchTab('dashboard')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 bg-slate-100 text-slate-800"
          >
            仪表盘
          </button>
          <button
            id="tab-btn-activation"
            onclick="switchTab('activation')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            设备激活
          </button>
          <button
            id="tab-btn-tools"
            onclick="switchTab('tools')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            工具
          </button>
          <button
            id="tab-btn-system"
            onclick="switchTab('system')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            系统
          </button>
        </div>
      </nav>

      <!-- 仪表盘标签页 -->
      <div id="dashboard-tab" class="tab-content active">
        <!-- 状态卡片网格 -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- 设备状态 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">设备状态</h3>
                <p class="text-sm text-slate-500">激活状态</p>
              </div>
            </div>
            <div class="text-center">
              <h4
                id="status-text"
                class="text-xl font-semibold text-red-600 mb-1"
              >
                未激活
              </h4>
              <p id="status-detail" class="text-sm text-slate-500">
                请输入激活码以启用设备功能
              </p>
            </div>
          </div>

          <!-- 连接状态 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">连接状态</h3>
                <p class="text-sm text-slate-500">服务器连接</p>
              </div>
            </div>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">服务器</span>
                <div id="connection-status" class="flex items-center gap-2">
                  <div class="w-2 h-2 bg-red-500 rounded-full status-dot"></div>
                  <span class="text-sm font-medium text-red-600">未连接</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">延迟</span>
                <span
                  id="network-latency"
                  class="text-sm font-medium text-slate-800"
                  >-</span
                >
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">最后同步</span>
                <span id="last-sync" class="text-sm text-slate-500">从未</span>
              </div>
            </div>
            <button
              onclick="testServerConnection()"
              class="w-full mt-4 px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors"
            >
              测试连接
            </button>
          </div>

          <!-- 系统信息 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">系统信息</h3>
                <p class="text-sm text-slate-500">性能监控</p>
              </div>
            </div>
            <div class="space-y-3">
              <div>
                <div class="flex justify-between items-center mb-1">
                  <span class="text-sm text-slate-600">CPU</span>
                  <span id="cpu-text" class="text-sm font-medium">0%</span>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2">
                  <div
                    id="cpu-progress"
                    class="bg-gradient-to-r from-emerald-400 to-emerald-500 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between items-center mb-1">
                  <span class="text-sm text-slate-600">内存</span>
                  <span id="memory-text" class="text-sm font-medium">0%</span>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2">
                  <div
                    id="memory-progress"
                    class="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备激活标签页 -->
      <div id="activation-tab" class="tab-content">
        <div class="max-w-md mx-auto">
          <div class="card rounded-2xl p-8 shadow-sm text-center">
            <div
              class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                />
              </svg>
            </div>

            <!-- 激活表单 -->
            <div id="activation-form">
              <h3 class="text-xl font-semibold text-slate-800 mb-2">
                设备激活
              </h3>
              <p class="text-slate-500 mb-6">请输入激活码以启用设备功能</p>

              <div class="space-y-4">
                <input
                  type="text"
                  id="activation-code"
                  placeholder="请输入激活码"
                  class="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
                <button
                  onclick="validateActivation()"
                  class="w-full px-6 py-3 bg-emerald-500 text-white font-medium rounded-xl hover:bg-emerald-600 transition-colors"
                >
                  激活设备
                </button>
              </div>
            </div>

            <!-- 已激活信息 -->
            <div id="activated-info" class="hidden">
              <h3 class="text-xl font-semibold text-emerald-600 mb-2">
                设备已激活
              </h3>
              <p class="text-slate-500 mb-4">设备功能已启用</p>
              <div
                id="activation-details"
                class="text-sm text-slate-600 bg-slate-50 rounded-lg p-4"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具标签页 -->
      <div id="tools-tab" class="tab-content">
        <div class="grid md:grid-cols-2 gap-6">
          <!-- 清理工具 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h6a1 1 0 100-2H7z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">清理工具</h3>
                <p class="text-sm text-slate-500">清理系统缓存</p>
              </div>
            </div>
            <p class="text-sm text-slate-600 mb-4">
              清理Cursor相关的缓存文件和临时数据，释放磁盘空间。
            </p>
            <button
              onclick="performCleanup()"
              class="w-full px-4 py-2 bg-orange-500 text-white font-medium rounded-lg hover:bg-orange-600 transition-colors"
            >
              开始清理
            </button>
          </div>
          <!-- 扩展信息 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">扩展信息</h3>
                <p class="text-sm text-slate-500">Augment扩展</p>
              </div>
            </div>
            <div id="augment-info" class="text-sm text-slate-600 mb-4">
              <p>正在获取扩展信息...</p>
            </div>
            <button
              onclick="getAugmentInfo()"
              class="w-full px-4 py-2 bg-cyan-500 text-white font-medium rounded-lg hover:bg-cyan-600 transition-colors"
            >
              刷新信息
            </button>
          </div>

          <!-- 更新检查 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">检查更新</h3>
                <p class="text-sm text-slate-500">应用程序更新</p>
              </div>
            </div>
            <p class="text-sm text-slate-600 mb-4">
              检查并安装最新版本的设备管理器。
            </p>
            <button
              onclick="checkForUpdates()"
              class="w-full px-4 py-2 bg-indigo-500 text-white font-medium rounded-lg hover:bg-indigo-600 transition-colors"
            >
              检查更新
            </button>
          </div>
        </div>
      </div>

      <!-- 系统标签页 -->
      <div id="system-tab" class="tab-content">
        <div class="grid lg:grid-cols-2 gap-6">
          <!-- 系统性能 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">系统性能</h3>
                <p class="text-sm text-slate-500">实时监控</p>
              </div>
            </div>

            <div class="space-y-6">
              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >CPU使用率</span
                  >
                  <span
                    id="cpu-text-detail"
                    class="text-sm font-semibold text-slate-900"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="cpu-progress-detail"
                    class="bg-gradient-to-r from-emerald-400 to-emerald-500 h-3 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>

              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >内存使用率</span
                  >
                  <span
                    id="memory-text-detail"
                    class="text-sm font-semibold text-slate-900"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="memory-progress-detail"
                    class="bg-gradient-to-r from-blue-400 to-blue-500 h-3 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>

              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >磁盘使用率</span
                  >
                  <span
                    id="disk-text"
                    class="text-sm font-semibold text-slate-900"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="disk-progress"
                    class="bg-gradient-to-r from-purple-400 to-purple-500 h-3 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 设备信息 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">设备信息</h3>
                <p class="text-sm text-slate-500">硬件详情</p>
              </div>
            </div>

            <div id="device-info" class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-slate-600">操作系统</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">处理器</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">总内存</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">可用内存</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
            </div>

            <button
              onclick="loadDeviceInfo()"
              class="w-full mt-4 px-4 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-colors"
            >
              刷新信息
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center mt-16 py-8 border-t border-slate-200">
      <p class="text-slate-400 text-sm">© 2025 Augment Team. 保留所有权利.</p>
    </footer>

    <script src="renderer.js"></script>
  </body>
</html>
