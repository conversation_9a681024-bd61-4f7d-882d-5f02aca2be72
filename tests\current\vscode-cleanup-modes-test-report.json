{"timestamp": "2025-07-14T14:11:48.029Z", "results": {"intelligent": {"success": true, "actions": ["🔄 第1步：清理前关闭相关IDE，避免文件占用问题", "🧪 [DRY RUN] forceCloseCursorIDE() - 执行模拟操作", "🔄 正在强制关闭VS Code IDE...", "⚠️ 强制关闭VS Code可能不完整: Command failed: taskkill /f /im \"Code.exe\" /t\n����: û���ҵ����� \"Code.exe\"��\r\n", "⏳ 等待5秒确保所有IDE进程完全终止...", "✅ IDE关闭完成，可以安全进行清理操作", "🔍 检查增强防护状态...", "🛑 检测到增强防护正在运行，清理前先停止防护...", "🛑 增强设备ID守护进程已停止", "✅ 增强防护已停止，可以安全进行清理", "🧠 使用智能清理模式 - 精准清理设备身份", "🧠 开始智能清理 - 精准清理设备身份数据", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护IDE设置: settings.json", "⚠️ 跳过非JSON设置文件: keybindings.json", "⚠️ 保护代码片段失败: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\snippets\\javascript.json: Expected property name or '}' in JSON at position 3 (line 2 column 2)", "✅ IDE设置保护完成，共保护 1 项配置", "🛡️ 已保护工作区配置: 11 个工作区", "🧪 [DRY RUN] cleanDeviceIdentityOnly() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentDeviceIdentity() - 执行模拟操作", "🧪 [DRY RUN] regenerateDeviceFingerprint() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复IDE设置: settings.json", "✅ IDE设置恢复完成", "🔄 已恢复工作区配置: 097770693c81a8cfb8d6d8f0fc4b69ce", "🔄 已恢复工作区配置: 23a4e91d2afc44513c23e3d98860cd26", "🔄 已恢复工作区配置: 296be1cf513897dee9741e519a42b9a7", "🔄 已恢复工作区配置: 2f8a2dffa33af6fbf7a1a1ebfd3f4f59", "🔄 已恢复工作区配置: 51d5d32f52c3b75c8ab21de4294c065c", "🔄 已恢复工作区配置: 65dddf11bb21e202d6c7f693db14ac97", "🔄 已恢复工作区配置: 75dc36bd0dc1fc681f51c8e7bde0b0ee", "🔄 已恢复工作区配置: 875ed0541d2c98fe6dd9cbbd11d79c75", "🔄 已恢复工作区配置: a31cb83a9d95ba71c46c4836db056263", "🔄 已恢复工作区配置: a807ce2792258e423bb2244bc458740f", "🔄 已恢复工作区配置: f43bbf7c98c481fd295f91bf4be60afd", "✅ 工作区配置恢复完成", "⚠️ 降级到内置守护进程模式", "🛡️ 内置守护进程已启动（客户端运行时防护）", "🎯 目标设备ID: ee1fb73b-8522-4b69-9736-a4e63884f388", "🔒 已启用零容忍备份文件监控", "🗄️ 已启用SQLite数据库监控", "🛡️ 已启用增强文件保护", "🔵 智能清理模式 - 处理VS Code设备身份", "🧠 VS Code stable - 智能清理设备身份", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 VS Code stable - 已更新设备ID: telemetry.devDeviceId", "🔄 VS Code stable - 已更新设备ID: telemetry.machineId", "🔄 VS Code stable - 已更新设备ID: telemetry.sqmId", "✅ VS Code stable - 设备身份已更新，扩展将识别为新用户", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "✅ VS Code stable - 智能清理完成，所有配置已保护", "🚀 最后步骤：重新启动IDE，应用清理结果", "🚀 正在启动Cursor IDE...", "✅ Cursor IDE已启动: E:\\cursor\\Cursor.exe", "⏳ Cursor IDE启动中，请稍候...", "🚀 正在启动VS Code IDE...", "✅ VS Code IDE已启动: C:\\Program Files\\Microsoft VS Code\\Code.exe", "⏳ VS Code IDE启动中，请稍候...", "✅ IDE重启完成，新的设备身份已生效", "✅ 智能清理完成 - 设备身份已重置，所有配置已保留", "🛡️ 保护范围: MCP配置 + IDE设置 + 工作区配置 + 登录状态", "🎯 效果: 扩展识别为新用户，但保留所有个人配置", "⚠️ 重要提醒: 智能模式仅更新设备身份，不清理任何IDE配置文件"], "errors": ["启动独立守护服务失败: 守护服务已在运行"], "options": {"intelligentMode": true, "cleanCursor": true, "cleanVSCode": true, "preserveActivation": true, "deepClean": false, "cleanCursorExtension": false, "autoRestartCursor": false, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": true, "resetCursorCompletely": false, "resetVSCodeCompletely": false, "aggressiveMode": false, "multiRoundClean": false, "extendedMonitoring": false, "usePowerShellAssist": false}}, "standard": {"success": true, "actions": ["🔄 第1步：清理前关闭相关IDE，避免文件占用问题", "🧪 [DRY RUN] forceCloseCursorIDE() - 执行模拟操作", "🔄 正在强制关闭VS Code IDE...", "⚠️ 强制关闭VS Code可能不完整: Command failed: taskkill /f /im \"Code.exe\" /t\n����: û���ҵ����� \"Code.exe\"��\r\n", "⏳ 等待5秒确保所有IDE进程完全终止...", "✅ IDE关闭完成，可以安全进行清理操作", "🔍 检查增强防护状态...", "🛑 检测到增强防护正在运行，清理前先停止防护...", "🛑 增强设备ID守护进程已停止", "✅ 增强防护已停止，可以安全进行清理", "🔧 使用标准清理模式 - 深度清理保留核心配置", "🔧 开始标准清理 - 深度清理保留核心配置", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 使用标准清理模式", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🧪 [DRY RUN] cleanActivationData() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentStorage() - 执行模拟操作", "🧪 [DRY RUN] cleanStateDatabase() - 执行模拟操作", "🧪 [DRY RUN] cleanWindowsRegistry() - 执行模拟操作", "🧪 [DRY RUN] cleanTempFiles() - 执行模拟操作", "🧪 [DRY RUN] cleanBrowserData() - 执行模拟操作", "🧪 [DRY RUN] cleanCursorExtensionData() - 执行模拟操作", "🧪 [DRY RUN] performVSCodeCleanup() - 执行模拟操作", "🧪 [DRY RUN] regenerateDeviceFingerprint() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "✅ 标准清理完成 - MCP配置已保护", "⚠️ 降级到内置守护进程模式", "🛡️ 内置守护进程已启动（客户端运行时防护）", "🎯 目标设备ID: 0ac4d3fe-a3d7-4ac6-9b8a-85fcf06ee7cd", "🔒 已启用零容忍备份文件监控", "🗄️ 已启用SQLite数据库监控", "🛡️ 已启用增强文件保护", "🚀 最后步骤：重新启动IDE，应用清理结果", "🚀 正在启动Cursor IDE...", "✅ Cursor IDE已启动: E:\\cursor\\Cursor.exe", "⏳ Cursor IDE启动中，请稍候...", "🚀 正在启动VS Code IDE...", "✅ VS Code IDE已启动: C:\\Program Files\\Microsoft VS Code\\Code.exe", "⏳ VS Code IDE启动中，请稍候...", "✅ IDE重启完成，新的设备身份已生效", "✅ 标准清理完成 - 深度清理已完成，MCP配置已保护"], "errors": ["启动独立守护服务失败: 守护服务已在运行"], "options": {"standardMode": true, "cleanCursor": true, "cleanVSCode": true, "preserveActivation": true, "deepClean": true, "cleanCursorExtension": true, "autoRestartCursor": true, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": true, "resetCursorCompletely": false, "resetVSCodeCompletely": false, "aggressiveMode": true, "multiRoundClean": true, "extendedMonitoring": true, "usePowerShellAssist": true}}, "complete": {"success": true, "actions": ["🔄 第1步：清理前关闭相关IDE，避免文件占用问题", "🧪 [DRY RUN] forceCloseCursorIDE() - 执行模拟操作", "🔄 正在强制关闭VS Code IDE...", "⚠️ 强制关闭VS Code可能不完整: Command failed: taskkill /f /im \"Code.exe\" /t\n����: û���ҵ����� \"Code.exe\"��\r\n", "⏳ 等待5秒确保所有IDE进程完全终止...", "✅ IDE关闭完成，可以安全进行清理操作", "🔍 检查增强防护状态...", "🛑 检测到增强防护正在运行，清理前先停止防护...", "🛑 增强设备ID守护进程已停止", "✅ 增强防护已停止，可以安全进行清理", "💥 使用完全清理模式 - 彻底重置仅保护MCP", "💥 开始完全清理 - 彻底重置仅保护MCP配置", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🧪 [DRY RUN] forceCloseCursorIDE() - 执行模拟操作", "🧪 [DRY RUN] cleanActivationData() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentStorage() - 执行模拟操作", "🧪 [DRY RUN] cleanStateDatabase() - 执行模拟操作", "🧪 [DRY RUN] cleanWindowsRegistry() - 执行模拟操作", "🧪 [DRY RUN] cleanTempFiles() - 执行模拟操作", "🧪 [DRY RUN] cleanBrowserData() - 执行模拟操作", "🧪 [DRY RUN] performCompleteCursorReset() - 执行模拟操作", "🧪 [DRY RUN] performCompleteVSCodeReset() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "⚠️ 降级到内置守护进程模式", "🛡️ 内置守护进程已启动（客户端运行时防护）", "🎯 目标设备ID: 49573353-3e98-4de3-a41a-cb20a9a0b930", "🔒 已启用零容忍备份文件监控", "🗄️ 已启用SQLite数据库监控", "🛡️ 已启用增强文件保护", "🚀 最后步骤：重新启动IDE，应用清理结果", "🚀 正在启动Cursor IDE...", "✅ Cursor IDE已启动: E:\\cursor\\Cursor.exe", "⏳ Cursor IDE启动中，请稍候...", "🚀 正在启动VS Code IDE...", "✅ VS Code IDE已启动: C:\\Program Files\\Microsoft VS Code\\Code.exe", "⏳ VS Code IDE启动中，请稍候...", "✅ IDE重启完成，新的设备身份已生效", "✅ 完全清理完成 - IDE已彻底重置，MCP配置已恢复"], "errors": ["启动独立守护服务失败: 守护服务已在运行"], "options": {"completeMode": true, "cleanCursor": true, "cleanVSCode": true, "preserveActivation": true, "deepClean": true, "cleanCursorExtension": true, "autoRestartCursor": true, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": false, "resetCursorCompletely": true, "resetVSCodeCompletely": true, "aggressiveMode": true, "multiRoundClean": true, "extendedMonitoring": true, "usePowerShellAssist": true}}}, "summary": {"intelligent_vscode_support": true, "standard_vscode_support": true, "complete_vscode_support": true}}