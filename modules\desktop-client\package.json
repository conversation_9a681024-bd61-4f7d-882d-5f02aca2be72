{"name": "augment-device-manager", "version": "1.0.1", "description": "Augment设备限制管理工具", "main": "src/main.js", "author": "Augment Team", "scripts": {"dev": "electron .", "start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "build:fix": "electron-builder && node scripts/fix-ffmpeg.js", "fix-ffmpeg": "node scripts/fix-ffmpeg.js", "release": "electron-builder --publish=always", "version:patch": "node scripts/release.js version patch", "version:minor": "node scripts/release.js version minor", "version:major": "node scripts/release.js version major", "publish": "node scripts/release.js release"}, "build": {"appId": "com.augment.device-manager", "productName": "Augment设备管理器", "executableName": "Augment设备管理器", "directories": {"output": "dist-final"}, "files": ["src/**/*", "public/**/*", "node_modules/**/*"], "extraResources": [{"from": "../../shared", "to": "shared"}], "asarUnpack": ["**/node_modules/sql.js/**/*"], "publish": {"provider": "github", "owner": "<PERSON><PERSON>-<PERSON><PERSON>-feng-lang-li", "repo": "augment-device-manager"}, "win": {"target": "nsis", "publisherName": "Augment Team", "icon": "public/logo.png", "requestedExecutionLevel": "asInvoker", "extraFiles": [{"from": "node_modules/electron/dist", "to": ".", "filter": ["ffmpeg.dll"]}]}, "mac": {"target": "dmg", "icon": "public/logo.png"}, "linux": {"target": "AppImage", "icon": "public/logo.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Augment设备管理器", "runAfterFinish": true, "allowElevation": true}}, "dependencies": {"chokidar": "^4.0.3", "electron-updater": "^6.6.2", "fs-extra": "^11.2.0", "node-fetch": "^2.7.0", "node-machine-id": "^1.1.12", "regedit": "^5.1.3", "sql.js": "^1.13.0", "sudo-prompt": "^9.2.1", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}}