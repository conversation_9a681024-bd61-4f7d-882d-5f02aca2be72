{"name": "cursor-augment-solution", "version": "1.0.0", "description": "Cursor Augment设备限制解决方案", "main": "index.js", "scripts": {"dev": "echo 🚀 启动完整开发环境... && npm run install-all && concurrently --names \"后端,客户端\" --prefix-colors \"blue,green\" \"cd modules/admin-backend && npm run dev\" \"cd modules/desktop-client && npm run dev\"", "server:start": "echo 🚀 启动自动更新ngrok服务... && node scripts/server/start-server-with-auto-update.js", "server:stop": "echo 🛑 停止远程控制服务... && node scripts/server/stop-server.js", "build:release": "echo 🚀 本地构建应用... && npm run build:update-config && npm run build:status && node scripts/build-and-release.js", "build:release:publish": "echo 🚀 构建并发布到GitHub... && npm run build:update-config && npm run build:status && node scripts/build-and-release.js --publish", "build:update-config": "echo 🔄 更新github、本地、客户端ngrok配置... && node scripts/setup/update-build-config.js", "build:status": "echo 📊 检查配置github、本地、客户端, ngrok地址状态... && node scripts/setup/update-build-config.js --status", "cleanup": "echo 🧹 清理所有进程... && taskkill /F /IM node.exe 2>nul || echo 无node进程 && taskkill /F /IM ngrok.exe 2>nul || echo 无ngrok进程 && echo ✅ 清理完成", "workflow:rebuild": "echo 🧹 清理ngrok进程... && taskkill /IM ngrok.exe /F 2>nul || echo 无ngrok进程需要清理 && echo 🚀 完整重建工作流程... && node scripts/workflow/rebuild-client.js", "build:force": "echo 🔥 强制构建（忽略Git状态）... && node scripts/build-and-release.js --force", "config:update": "echo 🔄 更新客户端配置... && node scripts/setup/update-client-config.js", "config:verify": "echo 🔍 验证配置状态... && node scripts/test/verify-config.js", "test:workflow": "echo 🧪 用户工作流程测试... && node scripts/test/test-user-workflow.js", "test:github": "echo 🧪 测试GitHub配置... && node scripts/test/test-github-config.js", "test:device-id": "node tests/current/test-stable-device-id.js", "setup:tokens": "echo 🔑 配置Token... && node scripts/setup/setup-tokens.js", "check:env": "echo 🔍 环境检测... && node scripts/utils/check-environment.js", "debug:status": "node scripts/debug/check-real-status.js", "fix:device-id": "node scripts/fix/fix-device-id-mismatch.js", "clean:full": "echo 🧹 完整清理（构建文件+数据+缓存）... && node scripts/build/clean-build.js && npm run data:clear-force && npm cache clean --force && echo ✅ 完整清理完成！", "data:clear": "echo 🗑️ 清理服务端数据... && cd modules/admin-backend && npm run clear-data", "data:clear-force": "echo 🗑️ 强制清理服务端数据... && cd modules/admin-backend && npm run clear-data-force", "stop:all-node": "node modules/desktop-client/test/fix-process-management.js --full", "stop:all-client": "node modules/desktop-client/test/fix-process-management.js --quick", "organize": "echo 🗂️ 整理根目录文件... && node scripts/utils/organize-root-files.js", "restore": "echo 🔄 恢复根目录文件... && node scripts/utils/restore-root-files.js", "workflow:quick": "echo ⚡ 快速重建... && npm run config:update && cd modules/desktop-client && npm run build", "install-all": "npm install && cd modules/admin-backend && npm install && cd ../desktop-client && npm install", "server-only": "echo 🌐 启动远程控制服务器... && cd modules/admin-backend && npm run dev", "quick-start": "echo ⚡ 快速启动（跳过依赖检查）... && concurrently --names \"后端,客户端\" --prefix-colors \"blue,green\" \"cd modules/admin-backend && npm run dev\" \"cd modules/desktop-client && npm run dev\"", "build-and-deploy": "echo 🚀 构建并部署... && npm run build:release && npm run server:start", "start": "npm run dev", "setup": "echo 📦 安装所有依赖... && npm run install-all && echo ✅ 安装完成！运行 npm run dev 启动开发环境", "server": "cd modules/admin-backend && npm run dev", "client": "cd modules/desktop-client && npm run dev", "full-deploy": "echo 🌐 完整部署流程... && npm run build:update-config && npm run build:release:publish && npm run server:start"}, "keywords": ["cursor", "augment", "device-limit", "activation"], "author": "Developer", "license": "MIT", "devDependencies": {"axios": "^1.10.0", "concurrently": "^9.2.0"}, "dependencies": {"chokidar": "^4.0.3", "dotenv": "^17.2.0", "fs-extra": "^11.3.0", "node-fetch": "^3.3.2", "sql.js": "^1.13.0"}}