# 命令启动智能清理说明文档

## 🚀 智能清理命令

### 基本命令

**智能清理选择 Cursor 并启动增强防护：**

```bash
node scripts/cleanup/cleanup-and-start-guardian.js cursor
```

**智能清理选择 VSCode 并启动增强防护：**

```bash
node scripts/cleanup/cleanup-and-start-guardian.js vscode
```

## 🛑 停止防护命令

### 完全停止防护

**完全停止所有增强防护：**

```bash
node scripts/utils/stop-enhanced-guardian.js
```

**停止所有守护进程：**

```bash
node scripts/startup/stop-guardian-processes.js
```

**停止守护进程（简化版）：**

```bash
node scripts/utils/stop-guardian-processes.js
```

### 使用 npm 脚本

**停止所有 Node.js 进程：**

```bash
npm run stop:all-node
```

**快速停止客户端进程：**

```bash
npm run stop:all-client
```

## ⚠️ 常见问题与解决方案

### 问题 1：wmic 命令失败

**症状：**

- 错误信息：`XML 无效` 或编码问题
- 进程查询失败
- 很多操作返回失败

**解决方案：**

1. **使用 PowerShell 执行（推荐）**

   ```powershell
   # 打开PowerShell管理员模式
   cd "C:\Users\<USER>\Desktop\augment-device-manager"
   node scripts/cleanup/cleanup-and-start-guardian.js cursor
   ```

2. **使用 CMD 执行**

   ```cmd
   cd /d "C:\Users\<USER>\Desktop\augment-device-manager"
   node scripts/cleanup/cleanup-and-start-guardian.js cursor
   ```

3. **修复 WMI 服务**
   ```powershell
   # 在PowerShell管理员模式下执行
   winmgmt /resetrepository
   net stop winmgmt
   net start winmgmt
   ```

### 问题 2：Git Bash 兼容性问题

**症状：**

- 路径解析错误（如 `/FI` 被解析为 Git 路径）
- Windows 命令执行异常

**解决方案：**

- ❌ 不要使用 Git Bash 执行这些命令
- ✅ 使用 PowerShell 或 CMD 执行

### 问题 3：进程冲突

**症状：**

- 多个 Node.js 进程运行
- 防护进程无法正常启动或停止

**解决方案：**

```powershell
# 1. 先停止所有防护
node scripts/utils/stop-enhanced-guardian.js

# 2. 等待进程完全停止
Start-Sleep -Seconds 3

# 3. 执行清理
node scripts/cleanup/cleanup-and-start-guardian.js cursor
```

## 🎯 推荐执行流程

### 完整清理流程

1. **打开 PowerShell 管理员模式**
2. **进入项目目录**
   ```powershell
   cd "C:\Users\<USER>\Desktop\augment-device-manager"
   ```
3. **停止现有防护**
   ```powershell
   node scripts/utils/stop-enhanced-guardian.js
   ```
4. **等待进程停止**
   ```powershell
   Start-Sleep -Seconds 3
   ```
5. **执行智能清理**
   ```powershell
   node scripts/cleanup/cleanup-and-start-guardian.js cursor
   ```

### 验证执行结果

**成功标志：**

- ✅ 停止现有防护进程
- ✅ 执行智能清理
- ✅ 生成新的设备 ID（符合 UUID v4 格式）
- ✅ 启动增强防护
- ✅ 验证防护状态

**失败处理：**

- 如果仍有问题，请检查完整的错误日志
- 考虑重启计算机后重新执行
- 确保以管理员权限运行

## 📋 ID 格式验证

### 正确的 ID 格式

**devDeviceId（设备 ID）：**

- 格式：标准 UUID v4 格式（36 字符）
- 示例：`78777087-3fc5-438b-9cdf-db8c78737be2`
- 特征：第 13 位是`4`，第 17 位是`8`,`9`,`a`,`b`之一

**machineId（机器 ID）：**

- 格式：64 位十六进制字符串（64 字符）
- 示例：`6b7e123498fc6402d83ff183133476b51d44ffdd0c11775c7f4ab02ec589a967`

**sqmId（仅 Cursor）：**

- 格式：大括号包围的大写 UUID（38 字符）
- 示例：`{6B5B6194-3E63-4BC0-B149-44C98BC704F2}`

### 验证 ID 格式

**检查生成的 ID 格式：**

```bash
node tests/current/test_id_fixes.js
```

**修复错误格式的 ID：**

```bash
node scripts/fix/fix-id-format-issues.js
```

## 💡 使用建议

1. **优先使用 PowerShell**：避免 Git Bash 的兼容性问题
2. **管理员权限**：确保有足够权限管理进程和文件
3. **逐步执行**：先停止防护，再执行清理
4. **验证结果**：执行后检查 ID 格式和防护状态
5. **保留日志**：如有问题，保存完整的错误日志用于分析

---

**最后更新：** 2025-01-25 **版本：** v2.0 - 包含问题诊断和解决方案
