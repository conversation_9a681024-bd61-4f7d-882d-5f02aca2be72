# 客户端防护测试指南

## 🎯 测试目标

在客户端界面中测试增强防护功能，验证：
1. 防护启动和状态显示
2. 实时文件监听和拦截
3. 统计数据更新
4. UI状态同步

## 📋 测试步骤

### 1. 启动客户端

```bash
# 在项目根目录执行
cd modules/desktop-client
npm start
```

### 2. 启动防护服务

1. **打开客户端界面**
2. **找到增强防护区域**（通常在主界面下方）
3. **点击"启动防护"按钮**
4. **观察状态变化**：
   - 按钮文字变为"停止防护"
   - 状态指示器变为绿色
   - 显示"独立服务运行中"或"内置进程运行中"

### 3. 验证防护状态

**检查状态显示**：
- ✅ 状态指示器：绿色圆点
- ✅ 状态文字：显示运行模式
- ✅ 统计数据：显示拦截次数、删除次数、恢复次数

**检查详细面板**：
- 防护模式（standalone/inprocess）
- 进程PID
- 目标设备ID
- 启动时间

### 4. 测试文件监听功能

#### 方法1：手动修改storage.json文件

1. **打开文件**：
   ```
   C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json
   ```

2. **查看当前设备ID**：
   ```json
   {
     "telemetry.devDeviceId": "当前的设备ID"
   }
   ```

3. **修改设备ID**：
   - 将设备ID改为任意其他值，如：`"test-modified-123"`
   - 保存文件

4. **观察客户端反应**：
   - **1-3秒内**应该看到拦截统计增加
   - 状态面板可能显示最近的拦截记录
   - 重新打开文件，设备ID应该被恢复

#### 方法2：使用测试脚本

在客户端运行时，打开新的命令行窗口：

```bash
cd modules/desktop-client
node test/trigger-monitoring.js
```

观察客户端界面的统计数据变化。

### 5. 观察UI更新

**实时统计更新**：
- 🚨 **拦截次数**：每次成功拦截设备ID修改时+1
- 🗑️ **删除次数**：删除备份文件时+1  
- 🔒 **恢复次数**：恢复保护时+1

**最近拦截记录**：
- 显示最近5次拦截操作
- 包含时间戳和操作类型
- 实时更新

### 6. 测试不同场景

#### 场景1：直接修改storage.json
```json
// 修改前
{"telemetry.devDeviceId": "原始ID"}

// 修改为
{"telemetry.devDeviceId": "测试ID"}

// 预期：1-3秒内被恢复为原始ID，拦截次数+1
```

#### 场景2：创建临时文件（模拟IDE行为）
```bash
# 创建临时文件
copy "C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json" "C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json.vsctmp"

# 修改临时文件中的设备ID
# 预期：临时文件被检测并修复，或被删除
```

#### 场景3：删除storage.json文件
```bash
# 删除文件
del "C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json"

# 预期：文件被重新创建，恢复次数+1
```

## 🔍 预期结果

### 正常工作的防护应该：

1. **快速响应**：1-3秒内检测到文件变化
2. **准确拦截**：恢复正确的目标设备ID
3. **统计更新**：客户端界面实时显示统计数据
4. **状态同步**：UI状态与实际防护状态一致

### 统计数据含义：

- **拦截次数**：成功拦截IDE修改设备ID的次数
- **删除次数**：删除备份文件的次数
- **恢复次数**：恢复被删除或损坏的配置文件次数

## ⚠️ 故障排除

### 如果防护不工作：

1. **检查防护状态**：
   - 确保状态显示为"运行中"
   - 检查进程PID是否存在

2. **检查目标设备ID**：
   - 当前文件ID与目标ID相同时，不会触发拦截
   - 需要手动修改为不同值来测试

3. **检查文件权限**：
   - 确保有读写storage.json的权限
   - 检查文件是否被其他程序锁定

4. **查看日志**：
   ```bash
   # 检查防护日志
   type "%TEMP%\guardian-service.log"
   ```

### 如果UI不更新：

1. **手动刷新状态**：点击刷新按钮
2. **重启客户端**：关闭并重新打开客户端
3. **检查事件监听**：确保客户端正在监听防护事件

## 🎉 测试成功标志

- ✅ 防护服务成功启动
- ✅ 文件修改被快速检测和恢复
- ✅ 统计数据实时更新
- ✅ UI状态准确反映防护状态
- ✅ 最近拦截记录正确显示

完成这些测试后，你就可以确信防护系统在客户端中正常工作了！
