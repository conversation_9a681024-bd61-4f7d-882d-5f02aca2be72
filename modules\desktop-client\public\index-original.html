<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment设备管理器</title>
    <script src="tailwind.css"></script>
    <style>
      /* 极简风格动画 */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(8px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(-12px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      @keyframes glow {
        0%,
        100% {
          box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
        }
        50% {
          box-shadow: 0 0 20px rgba(99, 102, 241, 0.8),
            0 0 30px rgba(99, 102, 241, 0.6);
        }
      }

      @keyframes slideInDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      @keyframes slideOutUp {
        from {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
        to {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideOutRight {
        from {
          opacity: 1;
          transform: translateX(0);
        }
        to {
          opacity: 0;
          transform: translateX(20px);
        }
      }

      .animate-spin {
        animation: spin 1s linear infinite;
      }
      .animate-pulse {
        animation: pulse 2s infinite;
      }
      .animate-fadeIn {
        animation: fadeIn 0.3s ease-out;
      }
      .animate-slideUp {
        animation: slideUp 0.6s ease-out;
      }
      .animate-glow {
        animation: glow 2s ease-in-out infinite;
      }

      /* 渐变背景 */
      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .gradient-mesh {
        background: radial-gradient(
            circle at 20% 80%,
            rgba(120, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 119, 198, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 40%,
            rgba(120, 219, 255, 0.2) 0%,
            transparent 50%
          );
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 6px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }

      /* 数据卡片样式 - 移除悬停动效 */
      .data-card {
        backdrop-filter: blur(10px);
        /* 移除过渡动画 */
      }

      /* 智能Tooltip样式 */
      .smart-tooltip {
        position: fixed;
        background: rgba(15, 23, 42, 0.96);
        color: white;
        padding: 20px 24px;
        border-radius: 12px;
        font-size: 13px;
        line-height: 2.2;
        max-width: 480px;
        min-width: 360px;
        word-wrap: break-word;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        z-index: 10000;
        pointer-events: none;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.2s ease;
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        letter-spacing: 0.3px;
        text-align: left;
      }

      .smart-tooltip.show {
        opacity: 1;
        transform: scale(1);
      }

      .smart-tooltip::before {
        content: "";
        position: absolute;
        border: 5px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.9);
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
      }

      .smart-tooltip[data-placement="bottom"]::before {
        top: -10px;
        bottom: auto;
        border-top-color: transparent;
        border-bottom-color: rgba(0, 0, 0, 0.9);
      }

      .smart-tooltip[data-placement="left"]::before {
        top: 50%;
        left: auto;
        right: -10px;
        bottom: auto;
        transform: translateY(-50%);
        border-top-color: transparent;
        border-left-color: rgba(0, 0, 0, 0.9);
      }

      .smart-tooltip[data-placement="right"]::before {
        top: 50%;
        left: -10px;
        bottom: auto;
        transform: translateY(-50%);
        border-top-color: transparent;
        border-right-color: rgba(0, 0, 0, 0.9);
      }

      /* 标签页切换样式 */
      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      .tab-btn {
        cursor: pointer;
      }

      /* Loading 样式 */
      .loading {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.6) !important;
        backdrop-filter: blur(4px) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 9999 !important;
      }

      .loading.hidden {
        display: none !important;
      }

      .loading-content {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 16px !important;
        padding: 32px !important;
        text-align: center !important;
        max-width: 320px !important;
        margin: 16px !important;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
      }

      .loading-spinner {
        width: 64px !important;
        height: 64px !important;
        border: 4px solid #e5e7eb !important;
        border-top: 4px solid #6366f1 !important;
        border-radius: 50% !important;
        animation: spin 1s linear infinite !important;
        margin: 0 auto 24px auto !important;
      }

      /* 确保loading动画正常工作 */
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Loading淡入淡出效果 */
      .loading {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }

      .loading:not(.hidden) {
        opacity: 1;
      }

      /* 通知动画 */
      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes slideOutRight {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    </style>
    <script
      src="https://lib.youware.com/youware-lib.1747145198.js"
      id="yourware-lib"
    ></script>
  </head>
  <body class="min-h-screen bg-slate-50 gradient-mesh">
    <div class="container mx-auto px-4 py-6 max-w-7xl">
      <!-- 页面头部增强 -->
      <div class="text-center mb-8 animate-fadeIn relative">
        <!-- 右上角按钮组 -->
        <div class="absolute top-0 right-0 flex gap-3">
          <!-- 主题切换按钮 -->
          <button
            id="theme-toggle-btn"
            onclick="toggleTheme()"
            class="group relative p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white/90"
            title="切换到极简风格"
          >
            <svg
              class="w-6 h-6 text-gray-600 group-hover:text-indigo-600 transition-colors duration-300"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <!-- 工具提示 -->
            <div
              class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
            >
              切换到极简风格
              <div
                class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"
              ></div>
            </div>
          </button>

          <!-- 公告历史记录按钮 -->
          <button
            id="announcement-history-btn"
            onclick="toggleAnnouncementHistory()"
            class="group relative p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white/90"
            title="公告历史记录"
          >
            <svg
              class="w-6 h-6 text-gray-600 group-hover:text-indigo-600 transition-colors duration-300"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <!-- 工具提示 -->
            <div
              class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
            >
              公告历史记录
              <div
                class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"
              ></div>
            </div>
          </button>
        </div>

        <h1 class="text-5xl font-bold text-black mb-3">🖥️Augment设备管理器</h1>
        <p class="text-gray-600 text-xl mb-4">Cursor IDE 设备限制解决方案</p>

        <!-- 快速状态概览 -->
        <div
          id="quick-status"
          class="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg"
        >
          <div
            id="quick-status-indicator"
            class="w-3 h-3 bg-red-500 rounded-full animate-pulse"
          ></div>
          <span id="quick-status-text" class="text-sm font-medium text-gray-700"
            >设备未激活</span
          >
        </div>
      </div>

      <!-- 标签导航增强 -->
      <div class="mb-8 animate-fadeIn">
        <div
          class="bg-white/70 backdrop-blur-lg rounded-2xl p-2 shadow-lg border border-white/20"
        >
          <div class="flex">
            <button
              id="tab-btn-dashboard"
              onclick="switchTab('dashboard')"
              class="tab-btn flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl transition-all duration-300 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg transform"
            >
              <svg
                class="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                ></path>
              </svg>
              <span class="font-medium">仪表盘</span>
            </button>
            <button
              id="tab-btn-tools"
              onclick="switchTab('tools')"
              class="tab-btn flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl transition-all duration-300 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"
            >
              <svg
                class="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <span class="font-medium">工具</span>
            </button>
            <button
              id="tab-btn-system"
              onclick="switchTab('system')"
              class="tab-btn flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl transition-all duration-300 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"
            >
              <svg
                class="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"
                ></path>
              </svg>
              <span class="font-medium">系统</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 仪表盘标签页 -->
      <div id="dashboard-tab" class="tab-content active animate-slideUp">
        <!-- 顶部状态概览卡片 -->
        <div class="grid lg:grid-cols-4 gap-4 mb-8">
          <!-- 设备激活状态 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-4 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 mb-1">设备状态</p>
                <h3 id="status-text" class="text-lg font-bold text-red-600">
                  未激活
                </h3>
              </div>
              <div
                class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- 连接状态 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-4 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 mb-1">连接状态</p>
                <h3 class="text-lg font-bold text-red-600">未连接</h3>
              </div>
              <div
                class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- CPU使用率 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-4 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 mb-1">CPU使用率</p>
                <h3 id="cpu-text" class="text-lg font-bold text-blue-600">
                  0%
                </h3>
              </div>
              <div
                class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  ></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- 内存使用率 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-4 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 mb-1">内存使用率</p>
                <h3 id="memory-text" class="text-lg font-bold text-purple-600">
                  0%
                </h3>
              </div>
              <div
                class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 主要功能区域 -->
        <div class="grid lg:grid-cols-3 gap-6 mb-8">
          <!-- 设备激活面板 -->
          <div class="lg:col-span-2">
            <div
              class="bg-white/80 backdrop-blur-lg rounded-3xl p-6 shadow-xl border border-white/20 data-card"
            >
              <div class="flex items-center mb-6">
                <div
                  class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mr-4"
                >
                  <svg
                    class="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold text-gray-800">设备激活</h3>
                  <p class="text-gray-600">Cursor IDE 授权管理</p>
                </div>
              </div>

              <!-- 激活表单区域 -->
              <div id="activation-form">
                <div class="space-y-4">
                  <div>
                    <label
                      for="activation-code"
                      class="block text-sm font-medium text-gray-700 mb-2"
                      >激活码</label
                    >
                    <input
                      type="text"
                      id="activation-code"
                      placeholder="请输入您的激活码"
                      maxlength="50"
                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-white/90"
                    />
                  </div>
                  <button
                    id="validate-btn"
                    class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-6 rounded-xl font-medium hover:from-indigo-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                    onclick="validateActivation()"
                  >
                    <span
                      id="validate-text"
                      class="flex items-center justify-center gap-2"
                    >
                      <svg
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                          clip-rule="evenodd"
                        ></path>
                      </svg>
                      <span>验证激活码</span>
                    </span>
                  </button>
                </div>
              </div>

              <!-- 激活成功信息 -->
              <div id="activated-info" class="hidden">
                <div
                  class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-4"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"
                    >
                      <span class="text-white text-sm">✓</span>
                    </div>
                    <div>
                      <strong class="text-green-800 block">激活成功！</strong>
                      <span
                        id="activation-details"
                        class="text-green-700 text-sm"
                      ></span>
                    </div>
                  </div>
                </div>
                <button
                  class="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium hover:bg-gray-200 transition-colors duration-200"
                  onclick="checkActivationStatus()"
                >
                  <span class="flex items-center justify-center gap-2">
                    <svg
                      class="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>刷新状态</span>
                  </span>
                </button>
              </div>

              <!-- 激活指南 -->
              <div
                class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-4 mt-6"
              >
                <h4 class="font-medium text-gray-800 mb-3 text-center">
                  激活指南
                </h4>
                <div class="space-y-2 text-sm text-gray-600">
                  <div class="flex items-start gap-2">
                    <div
                      class="w-5 h-5 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                    >
                      <span class="text-indigo-600 text-xs font-bold">1</span>
                    </div>
                    <p>获取有效的激活码</p>
                  </div>
                  <div class="flex items-start gap-2">
                    <div
                      class="w-5 h-5 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                    >
                      <span class="text-indigo-600 text-xs font-bold">2</span>
                    </div>
                    <p>在输入框中粘贴激活码</p>
                  </div>
                  <div class="flex items-start gap-2">
                    <div
                      class="w-5 h-5 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"
                    >
                      <span class="text-indigo-600 text-xs font-bold">3</span>
                    </div>
                    <p>点击验证按钮完成激活</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统监控面板 -->
          <div>
            <div
              class="bg-white/80 backdrop-blur-lg rounded-3xl p-6 shadow-xl border border-white/20 data-card h-full"
            >
              <div class="flex items-center mb-6">
                <div
                  class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mr-4"
                >
                  <svg
                    class="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold text-gray-800">系统监控</h3>
                  <p class="text-gray-600">实时性能监控</p>
                </div>
              </div>

              <div class="space-y-4">
                <!-- CPU使用率 -->
                <div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700"
                      >CPU使用率</span
                    >
                    <span
                      id="cpu-text-detail"
                      class="text-sm font-bold text-gray-900"
                      >0%</span
                    >
                  </div>
                  <div
                    class="w-full bg-gray-200 rounded-full h-2 overflow-hidden"
                  >
                    <div
                      id="cpu-progress"
                      class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-500"
                      style="width: 0%"
                    ></div>
                  </div>
                </div>

                <!-- 内存使用率 -->
                <div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700"
                      >内存使用率</span
                    >
                    <span
                      id="memory-text-detail"
                      class="text-sm font-bold text-gray-900"
                      >0%</span
                    >
                  </div>
                  <div
                    class="w-full bg-gray-200 rounded-full h-2 overflow-hidden"
                  >
                    <div
                      id="memory-progress"
                      class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500"
                      style="width: 0%"
                    ></div>
                  </div>
                </div>

                <!-- 磁盘使用率 -->
                <div>
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700"
                      >磁盘使用率</span
                    >
                    <span id="disk-text" class="text-sm font-bold text-gray-900"
                      >0%</span
                    >
                  </div>
                  <div
                    class="w-full bg-gray-200 rounded-full h-2 overflow-hidden"
                  >
                    <div
                      id="disk-progress"
                      class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 transition-all duration-500"
                      style="width: 0%"
                    ></div>
                  </div>
                </div>

                <!-- 系统信息 -->
                <div
                  class="grid grid-cols-2 gap-3 pt-4 border-t border-gray-200"
                >
                  <div class="text-center p-2 bg-gray-50 rounded-lg">
                    <p class="text-xs text-gray-500">主机名</p>
                    <p
                      id="hostname-text"
                      class="text-sm font-medium text-gray-800"
                    >
                      -
                    </p>
                  </div>
                  <div class="text-center p-2 bg-gray-50 rounded-lg">
                    <p class="text-xs text-gray-500">运行时间</p>
                    <p
                      id="uptime-text"
                      class="text-sm font-medium text-gray-800"
                    >
                      -
                    </p>
                  </div>
                  <div class="text-center p-2 bg-gray-50 rounded-lg">
                    <p class="text-xs text-gray-500">CPU核心</p>
                    <p
                      id="cpu-count-text"
                      class="text-sm font-medium text-gray-800"
                    >
                      -
                    </p>
                  </div>
                  <div class="text-center p-2 bg-gray-50 rounded-lg">
                    <p class="text-xs text-gray-500">总内存</p>
                    <p
                      id="total-memory-text"
                      class="text-sm font-medium text-gray-800"
                    >
                      -
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能操作区域 -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
          <!-- 快速操作 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center mr-3"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">快速操作</h3>
                <p class="text-gray-600 text-sm">常用功能快捷入口</p>
              </div>
            </div>
            <div class="space-y-3">
              <button
                onclick="checkActivationStatus()"
                class="w-full flex items-center justify-center gap-2 bg-indigo-50 text-indigo-600 py-3 px-4 rounded-lg font-medium hover:bg-indigo-100 transition-all duration-200 border border-indigo-200"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-sm">刷新状态</span>
              </button>
              <button
                onclick="switchTab('tools')"
                class="w-full flex items-center justify-center gap-2 bg-purple-50 text-purple-600 py-3 px-4 rounded-lg font-medium hover:bg-purple-100 transition-all duration-200 border border-purple-200"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-sm">管理工具</span>
              </button>
            </div>
          </div>

          <!-- 连接状态 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">连接状态</h3>
                <p class="text-gray-600 text-sm">服务器连接监控</p>
              </div>
            </div>
            <div class="space-y-4">
              <div
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <span class="text-sm text-gray-600">服务器连接</span>
                <div id="connection-status" class="flex items-center gap-2">
                  <div
                    class="w-2 h-2 bg-red-500 rounded-full animate-pulse"
                  ></div>
                  <span class="text-sm font-medium text-red-600">未连接</span>
                </div>
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <span class="text-sm text-gray-600">最后同步</span>
                <span id="last-sync" class="text-sm font-medium text-gray-800"
                  >从未</span
                >
              </div>
              <div
                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <span class="text-sm text-gray-600">网络延迟</span>
                <span
                  id="network-latency"
                  class="text-sm font-medium text-gray-800"
                  >-</span
                >
              </div>
              <div class="mt-4">
                <button
                  onclick="testServerConnection()"
                  class="w-full px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all duration-200"
                >
                  测试服务器连接
                </button>
              </div>
            </div>
          </div>

          <!-- 系统信息卡片 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">系统信息</h3>
                <p class="text-gray-600 text-sm">设备详细信息</p>
              </div>
            </div>
            <div class="space-y-3">
              <button
                onclick="switchTab('system')"
                class="w-full flex items-center justify-center gap-2 bg-orange-50 text-orange-600 py-3 px-4 rounded-lg font-medium hover:bg-orange-100 transition-all duration-200 border border-orange-200"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  ></path>
                </svg>
                <span class="text-sm">查看系统信息</span>
              </button>
              <button
                onclick="checkForUpdates()"
                class="w-full flex items-center justify-center gap-2 bg-green-50 text-green-600 py-3 px-4 rounded-lg font-medium hover:bg-green-100 transition-all duration-200 border border-green-200"
              >
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-sm">检查更新</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 信息卡片网格 -->
        <div class="grid md:grid-cols-2 gap-6 mb-8">
          <!-- Augment扩展信息卡片 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">
                    Augment扩展
                  </h3>
                  <p class="text-sm text-gray-600">扩展状态监控</p>
                </div>
              </div>
              <svg
                class="w-5 h-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
                title="显示Cursor Augment扩展的详细信息"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div id="augment-info" class="space-y-3">
              <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>

          <!-- 版本信息卡片 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">版本信息</h3>
                  <p class="text-sm text-gray-600">应用程序详情</p>
                </div>
              </div>
              <svg
                class="w-5 h-5 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
                title="当前应用程序的版本和构建信息"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clip-rule="evenodd"
                ></path>
              </svg>
            </div>
            <div id="version-info" class="space-y-3">
              <div
                class="flex justify-between items-center py-2 border-b border-gray-100"
              >
                <span class="text-sm text-gray-600">应用名称</span>
                <span id="app-name" class="text-sm font-medium text-gray-800"
                  >加载中...</span
                >
              </div>
              <div
                class="flex justify-between items-center py-2 border-b border-gray-100"
              >
                <span class="text-sm text-gray-600">版本号</span>
                <span id="app-version" class="text-sm font-medium text-gray-800"
                  >加载中...</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 设备信息面板 -->
        <div
          class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
        >
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
              <div
                class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800">设备信息</h3>
                <p class="text-gray-600">当前设备的详细信息</p>
              </div>
            </div>
            <button
              class="bg-indigo-100 text-indigo-600 py-2 px-4 rounded-lg text-sm hover:bg-indigo-200 transition-colors flex items-center gap-2"
              onclick="loadDeviceInfo()"
            >
              <svg
                class="w-4 h-4"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <span>刷新</span>
            </button>
          </div>

          <div
            id="device-info"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具标签页 -->
      <div id="tools-tab" class="tab-content hidden">
        <div class="grid md:grid-cols-2 gap-8">
          <!-- 设备清理工具 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-8 shadow-lg border border-white/20 data-card"
          >
            <div class="text-center mb-8">
              <div
                class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-glow"
              >
                <svg
                  class="w-10 h-10 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                    clip-rule="evenodd"
                  ></path>
                  <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h6a1 1 0 100-2H7z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-gray-800 mb-3">
                设备清理工具
              </h3>
              <p class="text-gray-600 mb-6">
                清理设备指纹和使用记录，解除设备限制
              </p>

              <!-- 清理项目说明 -->
              <div
                class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 text-left"
              >
                <h4 class="font-medium text-gray-800 mb-3">将要清理的项目：</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-indigo-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>设备指纹信息</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-indigo-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>使用记录缓存</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-indigo-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>临时配置文件</span>
                  </li>
                </ul>
              </div>
            </div>
            <button
              class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-6 rounded-xl font-medium hover:from-indigo-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              onclick="performCleanup()"
            >
              <span
                id="cleanup-text"
                class="flex items-center justify-center gap-2"
              >
                <svg
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                    clip-rule="evenodd"
                  ></path>
                  <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h6a1 1 0 100-2H7z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>执行设备清理</span>
              </span>
            </button>
          </div>

          <!-- 重置使用计数 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-8 shadow-lg border border-white/20 data-card"
          >
            <div class="text-center mb-8">
              <div
                class="w-20 h-20 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-glow"
              >
                <svg
                  class="w-10 h-10 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-gray-800 mb-3">
                重置使用计数
              </h3>
              <p class="text-gray-600 mb-6">
                重置Augment扩展的使用计数和统计信息
              </p>

              <!-- 重置项目说明 -->
              <div
                class="bg-gradient-to-r from-blue-50 to-teal-50 rounded-xl p-4 mb-6 text-left"
              >
                <h4 class="font-medium text-gray-800 mb-3">将要重置的项目：</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-blue-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>使用次数计数器</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-blue-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>使用时间记录</span>
                  </li>
                  <li class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-blue-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>统计数据缓存</span>
                  </li>
                </ul>
              </div>
            </div>
            <button
              class="w-full bg-gradient-to-r from-blue-600 to-teal-600 text-white py-4 px-6 rounded-xl font-medium hover:from-blue-700 hover:to-teal-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              onclick="resetUsageCount()"
            >
              <span
                id="reset-text"
                class="flex items-center justify-center gap-2"
              >
                <svg
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span>重置使用计数</span>
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- 系统标签页 -->
      <div id="system-tab" class="tab-content hidden">
        <div class="grid md:grid-cols-2 gap-6">
          <!-- 系统监控 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center mb-6">
              <div
                class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mr-4"
              >
                <svg
                  class="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-800">系统状态</h3>
                <p class="text-gray-600">实时系统性能监控</p>
              </div>
            </div>

            <div class="space-y-6">
              <!-- CPU使用率 -->
              <div>
                <div class="flex justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700"
                    >CPU使用率</span
                  >
                  <span id="cpu-text" class="text-sm font-medium text-gray-700"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div
                    id="cpu-progress"
                    class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full relative transition-all duration-500"
                    style="width: 0%"
                  >
                    <div
                      class="absolute inset-0 bg-white rounded-full opacity-30"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 内存使用率 -->
              <div>
                <div class="flex justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700"
                    >内存使用率</span
                  >
                  <span
                    id="memory-text"
                    class="text-sm font-medium text-gray-700"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div
                    id="memory-progress"
                    class="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full relative transition-all duration-500"
                    style="width: 0%"
                  >
                    <div
                      class="absolute inset-0 bg-white rounded-full opacity-30"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 磁盘使用率 -->
              <div>
                <div class="flex justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700"
                    >磁盘使用率</span
                  >
                  <span id="disk-text" class="text-sm font-medium text-gray-700"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div
                    id="disk-progress"
                    class="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full relative transition-all duration-500"
                    style="width: 0%"
                  >
                    <div
                      class="absolute inset-0 bg-white rounded-full opacity-30"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 更新检查 -->
          <div
            class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 data-card"
          >
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center">
                <div
                  class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mr-4"
                >
                  <svg
                    class="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-semibold text-gray-800">应用更新</h3>
                  <p class="text-gray-600">检查最新版本</p>
                </div>
              </div>
              <button
                class="text-indigo-600 hover:text-indigo-800 transition-colors p-2 rounded-lg hover:bg-indigo-50"
                onclick="checkForUpdates()"
              >
                <svg
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </button>
            </div>

            <div id="update-result" class="mb-6">
              <div class="text-center py-6">
                <div
                  class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <p class="text-gray-600 mb-4">点击右上角按钮检查应用程序更新</p>
              </div>
            </div>

            <!-- 更新历史 -->
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-4">
                最近更新记录
              </h4>
              <div class="space-y-3">
                <div
                  class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-xl"
                >
                  <div class="flex justify-between items-start mb-2">
                    <div class="font-medium text-gray-800">版本 1.2.3</div>
                    <div class="text-sm text-gray-500">2025年6月15日</div>
                  </div>
                  <div class="text-sm text-gray-600">
                    修复了一些问题并改进了性能
                  </div>
                </div>
                <div
                  class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-xl"
                >
                  <div class="flex justify-between items-start mb-2">
                    <div class="font-medium text-gray-800">版本 1.2.2</div>
                    <div class="text-sm text-gray-500">2025年5月30日</div>
                  </div>
                  <div class="text-sm text-gray-600">
                    添加了新功能并修复了已知问题
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading hidden" id="loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p class="text-gray-700 font-medium text-lg">处理中，请稍候...</p>
        </div>
      </div>

      <!-- 公告历史记录弹窗 -->
      <div
        id="announcement-history-modal"
        class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden opacity-0 transition-all duration-300"
        onclick="closeAnnouncementHistory(event)"
      >
        <div class="flex items-center justify-center min-h-screen p-4">
          <div
            class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden transform scale-95 transition-all duration-300"
            onclick="event.stopPropagation()"
          >
            <!-- 弹窗头部 -->
            <div
              class="flex items-center justify-between p-6 border-b border-gray-200/50"
            >
              <div class="flex items-center gap-3">
                <div
                  class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800">公告历史记录</h3>
                  <p class="text-sm text-gray-600">查看所有历史广播消息</p>
                </div>
              </div>
              <button
                onclick="closeAnnouncementHistory()"
                class="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
              >
                <svg
                  class="w-6 h-6 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div id="announcement-history-content" class="space-y-4">
                <!-- 历史消息将通过JavaScript动态加载 -->
                <div class="text-center py-8 text-gray-500">
                  <svg
                    class="w-12 h-12 mx-auto mb-4 text-gray-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <p>正在加载历史公告...</p>
                </div>
              </div>
            </div>

            <!-- 弹窗底部 -->
            <div
              class="flex items-center justify-between p-6 border-t border-gray-200/50 bg-gray-50/50"
            >
              <div class="text-sm text-gray-500">
                <span id="announcement-count">0</span> 条历史公告
              </div>
              <button
                onclick="clearAnnouncementHistory()"
                class="px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
              >
                清空历史
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <div class="text-center mt-16 pt-8 border-t border-white/20">
        <p class="text-gray-500">© 2025 Augment Team. 保留所有权利.</p>
      </div>
    </div>

    <script>
      // 主题切换功能
      function toggleTheme() {
        // 切换到极简风格 (index.html)
        if (confirm("确定要切换到极简风格吗？")) {
          window.location.href = "index.html";
        }
      }

      // 智能Tooltip系统
      class SmartTooltip {
        constructor() {
          this.tooltip = null;
          this.currentTarget = null;
          this.showTimeout = null;
          this.hideTimeout = null;
          this.init();
        }

        init() {
          // 创建tooltip元素
          this.tooltip = document.createElement("div");
          this.tooltip.className = "smart-tooltip";
          document.body.appendChild(this.tooltip);

          // 绑定事件
          this.bindEvents();
        }

        bindEvents() {
          document.addEventListener("mouseover", (e) => {
            const target = e.target.closest("[title]");
            if (target && target.title && this.shouldShowTooltip(target)) {
              this.show(target, target.title);
            }
          });

          document.addEventListener("mouseout", (e) => {
            const target = e.target.closest("[title]");
            if (target === this.currentTarget) {
              this.hide();
            }
          });

          document.addEventListener("scroll", () => {
            if (this.currentTarget) {
              this.updatePosition();
            }
          });

          window.addEventListener("resize", () => {
            if (this.currentTarget) {
              this.updatePosition();
            }
          });
        }

        shouldShowTooltip(element) {
          // 检查是否需要显示tooltip
          const title = element.title.trim();
          if (!title) return false;

          // 检查文本是否溢出或被截断
          const textContent = element.textContent.trim();

          // 如果title和显示文本相同，检查是否有溢出
          if (title === textContent) {
            // 检查元素是否有文本溢出
            const hasOverflow =
              element.scrollWidth > element.clientWidth ||
              element.scrollHeight > element.clientHeight;
            return hasOverflow;
          }

          // 如果title和显示文本不同，说明文本被截断了，需要显示tooltip
          return true;
        }

        show(target, text) {
          if (this.showTimeout) clearTimeout(this.showTimeout);
          if (this.hideTimeout) clearTimeout(this.hideTimeout);

          this.currentTarget = target;
          this.tooltip.textContent = text;

          this.showTimeout = setTimeout(() => {
            this.updatePosition();
            this.tooltip.classList.add("show");
          }, 300);
        }

        hide() {
          if (this.showTimeout) clearTimeout(this.showTimeout);

          this.hideTimeout = setTimeout(() => {
            this.tooltip.classList.remove("show");
            this.currentTarget = null;
          }, 100);
        }

        updatePosition() {
          if (!this.currentTarget) return;

          const targetRect = this.currentTarget.getBoundingClientRect();
          const tooltipRect = this.tooltip.getBoundingClientRect();
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          let x, y;
          let placement = "top"; // 默认在上方

          // 计算最佳位置
          const positions = {
            top: {
              x: targetRect.left + targetRect.width / 2 - tooltipRect.width / 2,
              y: targetRect.top - tooltipRect.height - 10,
            },
            bottom: {
              x: targetRect.left + targetRect.width / 2 - tooltipRect.width / 2,
              y: targetRect.bottom + 10,
            },
            left: {
              x: targetRect.left - tooltipRect.width - 10,
              y:
                targetRect.top + targetRect.height / 2 - tooltipRect.height / 2,
            },
            right: {
              x: targetRect.right + 10,
              y:
                targetRect.top + targetRect.height / 2 - tooltipRect.height / 2,
            },
          };

          // 选择最佳位置（优先级：top > bottom > right > left）
          for (const pos of ["top", "bottom", "right", "left"]) {
            const position = positions[pos];
            if (
              position.x >= 8 &&
              position.x + tooltipRect.width <= viewportWidth - 8 &&
              position.y >= 8 &&
              position.y + tooltipRect.height <= viewportHeight - 8
            ) {
              x = position.x;
              y = position.y;
              placement = pos;
              break;
            }
          }

          // 如果没有合适位置，使用智能调整
          if (x === undefined || y === undefined) {
            x = Math.min(
              Math.max(
                8,
                targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
              ),
              viewportWidth - tooltipRect.width - 8
            );
            y = Math.min(
              Math.max(8, targetRect.top - tooltipRect.height - 10),
              viewportHeight - tooltipRect.height - 8
            );
          }

          this.tooltip.style.left = x + scrollX + "px";
          this.tooltip.style.top = y + scrollY + "px";
          this.tooltip.setAttribute("data-placement", placement);
        }
      }

      // 页面加载完成后的初始化
      document.addEventListener("DOMContentLoaded", function () {
        // 初始化智能Tooltip系统
        new SmartTooltip();

        // 更新主题切换按钮的提示文本
        const themeBtn = document.getElementById("theme-toggle-btn");
        if (themeBtn) {
          themeBtn.title = "切换到极简风格";
        }
      });
    </script>
    <script src="renderer.js"></script>
  </body>
</html>
