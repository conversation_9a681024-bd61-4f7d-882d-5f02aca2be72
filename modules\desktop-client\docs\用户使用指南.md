# Augment 设备管理器 - 用户使用指南

## 功能概述

Augment 设备管理器现在支持 Cursor IDE 和 VS Code 两种 IDE，提供灵活的清理模式：

### 🎨 Cursor IDE 支持

#### 🛡️ 默认清理模式（推荐）

- **仅清理 Augment 扩展数据** - 让 Augment 扩展认为是新设备
- **保留 Cursor IDE 登录状态** - 无需重新登录
- **保持用户设置** - 保留个人配置和偏好

#### 🔄 完全重置模式

- **完全清理 Cursor IDE 数据** - 需要重新登录
- **重置所有遥测 ID** - 让 Cursor IDE 也认为是全新用户
- **清除所有设置** - 恢复到首次安装状态

### 💙 VS Code 支持（新增）

#### 🛡️ 默认清理模式（推荐）

- **仅清理 Augment 扩展数据** - 让 Augment 扩展认为是新设备
- **保留 VS Code 登录状态** - 无需重新登录
- **支持多版本** - 自动检测 Stable、Insiders、OSS 版本
- **保持用户设置** - 保留个人配置和偏好

#### 🔄 完全重置模式

- **完全清理 VS Code 数据** - 需要重新登录
- **重置所有遥测 ID** - 让 VS Code 也认为是全新用户
- **清除所有设置** - 恢复到首次安装状态

## 使用方法

### 启动客户端

1. 双击运行 `desktop-client` 目录下的客户端程序
2. 等待界面加载完成

### 选择要清理的 IDE

1. **选择 IDE**：在"🎯 选择要清理的 IDE"区域中：
   - **🎨 Cursor IDE**：默认勾选，默认清理 Augment 扩展数据
   - **💙 Visual Studio Code**：可选勾选，默认清理 Augment 扩展数据
   - 可以同时选择两个 IDE 进行清理

### 选择清理模式

#### 默认模式（推荐日常使用）

**Cursor IDE 清理**：

1. 勾选"🎨 Cursor IDE"
2. **不勾选**"🔄 完全重置 Cursor IDE 用户身份"
3. 点击"开始清理"按钮
4. **结果**：Augment 扩展认为是新设备，Cursor IDE 保持登录状态

**VS Code 清理**：

1. 勾选"💙 Visual Studio Code"
2. **不勾选**"🔄 完全重置 VS Code 用户身份"
3. 点击"开始清理"按钮
4. **结果**：Augment 扩展认为是新设备，VS Code 保持登录状态

**混合清理**：

1. 同时勾选"🎨 Cursor IDE"和"💙 Visual Studio Code"
2. 根据需要选择各自的重置选项
3. 点击"开始清理"按钮
4. **结果**：两个 IDE 的 Augment 扩展都认为是新设备

#### 完全重置模式（彻底重置）

**Cursor IDE 完全重置**：

1. 勾选"🎨 Cursor IDE"
2. **勾选**"🔄 完全重置 Cursor IDE 用户身份"
3. 点击"开始清理"按钮
4. **结果**：Cursor IDE 和 Augment 扩展都认为是全新用户
5. **注意**：需要重新登录 Cursor IDE

**VS Code 完全重置**：

1. 勾选"💙 Visual Studio Code"
2. **勾选**"🔄 完全重置 VS Code 用户身份"
3. 点击"开始清理"按钮
4. **结果**：VS Code 和 Augment 扩展都认为是全新用户
5. **注意**：需要重新登录 VS Code

## 清理过程说明

### 清理步骤

1. **备份数据** - 自动备份重要文件
2. **清理激活数据** - 重置设备管理器激活状态
3. **清理 Augment 存储** - 清除扩展相关数据
4. **清理状态数据库** - 处理 SQLite 数据库
5. **清理扩展数据** - 重置扩展识别信息
6. **多轮深度清理** - 确保清理彻底
7. **持续监控** - 防止数据恢复
8. **重启 Cursor** - 应用更改

### 清理时间

- **默认模式**：约 30-60 秒
- **完全重置模式**：约 60-90 秒

## 清理结果验证

### 成功标志

- ✅ **设备 ID 更新成功** - 显示新的设备标识
- ✅ **清理操作完成** - 无严重错误
- ✅ **Cursor IDE 状态正常** - 根据选择的模式

### 默认模式验证

```
✅ 设备ID更新: 成功
✅ 登录状态保留: 成功
✅ Augment扩展重置: 成功
```

### 完全重置模式验证

```
✅ 设备ID更新: 成功
✅ Cursor IDE完全重置: 成功
✅ 需要重新登录: 是
```

## 常见问题

### Q: 什么时候使用默认模式？

**A**: 日常使用推荐默认模式，既能重置 Augment 扩展又保持 Cursor IDE 的便利性。

### Q: 什么时候使用完全重置模式？

**A**: 以下情况建议使用完全重置：

- 需要完全清除 Cursor IDE 使用痕迹
- 测试环境验证新用户体验
- 想要彻底重置所有设置

### Q: 完全重置后需要做什么？

**A**: 完全重置后需要：

1. 重新登录 Cursor IDE
2. 重新配置个人偏好设置
3. 重新安装需要的扩展

### Q: 清理失败怎么办？

**A**: 如果清理失败：

1. 检查 Cursor IDE 是否完全关闭
2. 以管理员权限运行客户端
3. 查看错误日志，联系技术支持

### Q: 数据安全吗？

**A**: 完全安全：

- 所有操作前都会自动备份
- 只清理指定的应用数据
- 不会影响系统文件或其他应用

## 技术支持

### 日志文件位置

- 清理日志保存在客户端目录的 `logs` 文件夹
- 备份文件保存在临时目录，包含时间戳

### 故障排除

1. **权限问题** - 以管理员身份运行
2. **文件占用** - 确保 Cursor IDE 完全关闭
3. **网络问题** - 检查网络连接状态

### 联系支持

如遇到问题，请提供：

- 错误截图或日志
- 使用的清理模式
- 系统环境信息

## 注意事项

### ⚠️ 重要提醒

- **完全重置会清除所有 Cursor IDE 数据**，请确认后再操作
- **建议先关闭 Cursor IDE**，避免文件占用冲突
- **网络环境稳定时使用**，确保清理过程不被中断

### 💡 使用建议

- **首次使用建议选择默认模式**，熟悉流程后再尝试完全重置
- **重要项目工作前建议手动备份**，虽然程序会自动备份
- **定期清理可以保持最佳效果**，建议每周使用一次

## 更新日志

### v2.0 新功能

- ✅ 新增 Cursor IDE 登录状态保留功能
- ✅ 新增完全重置 Cursor IDE 选项
- ✅ 优化清理流程，提高成功率
- ✅ 改进错误处理和用户反馈
- ✅ 增强数据安全和备份机制

### 兼容性

- ✅ 向后兼容，现有用户无需更改使用习惯
- ✅ 支持 Windows 10/11 系统
- ✅ 兼容最新版本的 Cursor IDE
