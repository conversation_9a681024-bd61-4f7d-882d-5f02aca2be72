<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment 激活码管理系统</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        /* 极简专业色彩系统 */
        --primary-50: #f8fafc;
        --primary-100: #f1f5f9;
        --primary-200: #e2e8f0;
        --primary-300: #cbd5e1;
        --primary-400: #94a3b8;
        --primary-500: #64748b;
        --primary-600: #475569;
        --primary-700: #334155;
        --primary-800: #1e293b;
        --primary-900: #0f172a;

        /* 功能色彩 */
        --success: #10b981;
        --success-light: #d1fae5;
        --warning: #f59e0b;
        --warning-light: #fef3c7;
        --error: #ef4444;
        --error-light: #fee2e2;
        --info: #3b82f6;
        --info-light: #dbeafe;

        /* 渐变 */
        --gradient-primary: linear-gradient(
          135deg,
          var(--primary-600) 0%,
          var(--primary-800) 100%
        );
        --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        --gradient-card: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);

        /* 阴影系统 */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);

        /* 边框半径 */
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-2xl: 1.5rem;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        background: var(--gradient-surface);
        min-height: 100vh;
        color: var(--primary-800);
        line-height: 1.6;
        font-size: 14px;
        font-weight: 400;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
      }

      .header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem 0;
      }

      .header h1 {
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary-900);
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
      }

      .header p {
        font-size: 1rem;
        color: var(--primary-600);
        font-weight: 400;
      }

      .login-form,
      .dashboard {
        background: var(--gradient-card);
        backdrop-filter: blur(20px);
        border-radius: var(--radius-2xl);
        padding: 3rem;
        box-shadow: var(--shadow-xl);
        margin-bottom: 2rem;
        border: 1px solid var(--primary-200);
        transition: all 0.2s ease;
      }

      .login-form {
        max-width: 420px;
        margin: 0 auto;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--primary-700);
        font-size: 0.875rem;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 1px solid var(--primary-300);
        border-radius: var(--radius-lg);
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--primary-800);
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.1);
        background: white;
      }

      .btn {
        background: var(--gradient-primary);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius-lg);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        text-align: center;
        gap: 0.5rem;
        box-shadow: var(--shadow-sm);
      }

      .btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
        opacity: 0.9;
      }

      .btn:active {
        transform: translateY(0);
      }

      .btn-secondary {
        background: var(--primary-500);
        color: white;
      }

      .btn-secondary:hover {
        background: var(--primary-600);
      }

      .btn-small {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
      }

      .btn-danger {
        background: var(--error);
      }

      .btn-danger:hover {
        background: #dc2626;
      }

      .btn-success {
        background: var(--success);
      }

      .btn-success:hover {
        background: #059669;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
      }

      .stat-card {
        background: white;
        border: 1px solid var(--primary-200);
        border-radius: var(--radius-xl);
        padding: 2rem;
        text-align: left;
        transition: all 0.2s ease;
        position: relative;
        box-shadow: var(--shadow-sm);
      }

      .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-300);
      }

      .stat-card h3 {
        font-size: 2rem;
        margin: 0.5rem 0;
        font-weight: 600;
        color: var(--primary-900);
      }

      .stat-card p {
        font-size: 0.875rem;
        color: var(--primary-600);
        margin: 0;
      }

      .stat-card .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      .tabs {
        display: flex;
        background: white;
        border-radius: var(--radius-xl);
        padding: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--primary-200);
        gap: 0.25rem;
      }

      .tab {
        flex: 1;
        padding: 0.875rem 1.5rem;
        background: transparent;
        border: none;
        border-radius: var(--radius-lg);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--primary-600);
        text-align: center;
      }

      .tab.active {
        background: var(--gradient-primary);
        color: white;
        box-shadow: var(--shadow-sm);
      }

      .tab:hover:not(.active) {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .data-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--primary-200);
      }

      .data-table th,
      .data-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid var(--primary-200);
        font-size: 0.875rem;
      }

      .data-table th {
        background: var(--primary-50);
        font-weight: 600;
        color: var(--primary-700);
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .data-table tbody tr:hover {
        background: var(--primary-50);
      }

      .status,
      .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: var(--radius-md);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
      }

      .status-active {
        background: var(--success-light);
        color: var(--success);
      }

      .status-used {
        background: var(--info-light);
        color: var(--info);
      }

      .status-expired {
        background: var(--error-light);
        color: var(--error);
      }

      .status-revoked {
        background: var(--warning-light);
        color: var(--warning);
      }

      .code-text {
        background: var(--primary-100);
        color: var(--primary-800);
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
          "Courier New", monospace;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .device-id {
        background: var(--primary-50);
        color: var(--primary-700);
        padding: 0.25rem 0.5rem;
        border-radius: var(--radius-sm);
        font-family: "SF Mono", Monaco, monospace;
        font-size: 0.75rem;
      }

      .datetime-text {
        color: var(--primary-600);
        font-size: 0.875rem;
      }

      .text-danger {
        color: var(--error) !important;
      }

      /* 操作按钮容器 */
      .action-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        align-items: center;
      }

      .hidden {
        display: none;
      }

      /* 提示框样式 */
      .alert {
        padding: 1rem 1.5rem;
        border-radius: var(--radius-lg);
        margin-bottom: 1.5rem;
        border: 1px solid;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .alert-success,
      .alert.success {
        background: var(--success-light);
        color: var(--success);
        border-color: var(--success);
      }

      .alert-success::before,
      .alert.success::before {
        content: "✓";
        font-weight: bold;
      }

      .alert-error,
      .alert.error {
        background: var(--error-light);
        color: var(--error);
        border-color: var(--error);
      }

      .alert-error::before,
      .alert.error::before {
        content: "⚠";
        font-weight: bold;
      }

      .alert-info {
        background: var(--info-light);
        color: var(--info);
        border-color: var(--info);
      }

      .alert-info::before {
        content: "ℹ";
        font-weight: bold;
      }

      /* 代码显示样式 */
      .code-display {
        background: var(--gradient-card);
        border: 1px solid var(--primary-300);
        padding: 1.5rem;
        border-radius: var(--radius-xl);
        font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
          "Courier New", monospace;
        font-size: 1.125rem;
        font-weight: 600;
        text-align: center;
        margin: 1.5rem 0;
        color: var(--primary-800);
        letter-spacing: 0.1em;
        position: relative;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease;
      }

      .code-display::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(102, 126, 234, 0.1),
          transparent
        );
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      /* 微交互动画 */
      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }

      .animate-slide-in {
        animation: slideInUp 0.4s ease-out;
      }

      .animate-pulse {
        animation: pulse 2s infinite;
      }

      /* 加载状态 */
      .loading {
        position: relative;
        pointer-events: none;
        opacity: 0.6;
      }

      .loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid var(--primary-300);
        border-top-color: var(--primary-600);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(100%);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideOutRight {
        from {
          opacity: 1;
          transform: translateX(0);
        }
        to {
          opacity: 0;
          transform: translateX(100%);
        }
      }

      .code-display:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
      }

      /* 数据表格样式 */
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        table-layout: fixed;
      }

      .data-table th,
      .data-table td {
        padding: 16px 20px;
        text-align: left;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        vertical-align: middle;
        word-wrap: break-word;
        overflow: hidden;
      }

      .data-table th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 700;
        color: #495057;
        white-space: nowrap;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .data-table tr {
        transition: all 0.2s ease;
      }

      .data-table tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
        transform: scale(1.01);
      }

      .data-table tr:last-child td {
        border-bottom: none;
      }

      /* 用户管理表格列宽 */
      .users-table th:nth-child(1),
      .users-table td:nth-child(1) {
        width: 25%;
      } /* 设备ID */
      .users-table th:nth-child(2),
      .users-table td:nth-child(2) {
        width: 20%;
      } /* 激活码 */
      .users-table th:nth-child(3),
      .users-table td:nth-child(3) {
        width: 15%;
      } /* 激活时间 */
      .users-table th:nth-child(4),
      .users-table td:nth-child(4) {
        width: 15%;
      } /* 过期时间 */
      .users-table th:nth-child(5),
      .users-table td:nth-child(5) {
        width: 15%;
      } /* 状态 */
      .users-table th:nth-child(6),
      .users-table td:nth-child(6) {
        width: 10%;
      } /* 操作 */

      /* 激活码列表表格列宽 */
      .codes-table th:nth-child(1),
      .codes-table td:nth-child(1) {
        width: 20%;
      } /* 激活码 */
      .codes-table th:nth-child(2),
      .codes-table td:nth-child(2) {
        width: 10%;
      } /* 状态 */
      .codes-table th:nth-child(3),
      .codes-table td:nth-child(3) {
        width: 25%;
      } /* 绑定设备 */
      .codes-table th:nth-child(4),
      .codes-table td:nth-child(4) {
        width: 12%;
      } /* 创建时间 */
      .codes-table th:nth-child(5),
      .codes-table td:nth-child(5) {
        width: 12%;
      } /* 过期时间 */
      .codes-table th:nth-child(6),
      .codes-table td:nth-child(6) {
        width: 8%;
      } /* 备注 */
      .codes-table th:nth-child(7),
      .codes-table td:nth-child(7) {
        width: 13%;
      } /* 操作 */

      /* 使用记录表格列宽 */
      .logs-table th:nth-child(1),
      .logs-table td:nth-child(1) {
        width: 15%;
      } /* 时间 */
      .logs-table th:nth-child(2),
      .logs-table td:nth-child(2) {
        width: 20%;
      } /* 激活码 */
      .logs-table th:nth-child(3),
      .logs-table td:nth-child(3) {
        width: 25%;
      } /* 设备ID */
      .logs-table th:nth-child(4),
      .logs-table td:nth-child(4) {
        width: 10%;
      } /* 操作 */
      .logs-table th:nth-child(5),
      .logs-table td:nth-child(5) {
        width: 30%;
      } /* 详情 */

      /* 状态标签 */
      .status-badge,
      .action-badge {
        padding: 3px 6px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        display: inline-block;
        margin: 1px 2px;
        white-space: nowrap;
      }

      /* 状态容器 */
      .status-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        align-items: center;
      }

      /* 操作按钮容器 */
      .action-container {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
        align-items: center;
      }

      .status-active {
        background: #d4edda;
        color: #155724;
      }

      .status-used {
        background: #cce5ff;
        color: #004085;
      }

      .status-expired {
        background: #f8d7da;
        color: #721c24;
      }

      .status-revoked {
        background: #fff3cd;
        color: #856404;
      }

      .status-inactive {
        background: #f8d7da;
        color: #721c24;
      }

      .action-created {
        background: #d1ecf1;
        color: #0c5460;
      }

      .action-activated {
        background: #d4edda;
        color: #155724;
      }

      .action-failed {
        background: #f8d7da;
        color: #721c24;
      }

      .action-revoked {
        background: #fff3cd;
        color: #856404;
      }

      .action-verified {
        background: #cce5ff;
        color: #004085;
      }

      /* 小按钮样式 */
      .btn-small {
        padding: 3px 6px;
        font-size: 11px;
        margin: 1px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
        min-width: 40px;
        text-align: center;
      }

      .btn-small:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-danger {
        background: #dc3545;
        color: white;
      }

      .btn-warning {
        background: #ffc107;
        color: #212529;
      }

      .btn-success {
        background: #28a745;
        color: white;
      }

      /* 代码文本样式 */
      .code-text {
        font-family: "Courier New", monospace;
        background: #e3f2fd;
        border: 1px solid #90caf9;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 10px;
        color: #1565c0;
        font-weight: 600;
        word-break: break-all;
        max-width: 100%;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .device-id {
        font-family: "Courier New", monospace;
        background: #f3e5f5;
        border: 1px solid #ce93d8;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 10px;
        color: #7b1fa2;
        font-weight: 600;
        word-break: break-all;
        max-width: 100%;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* 代码容器样式 */
      .code-container {
        display: flex;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
      }

      /* 时间显示样式 */
      .datetime-text {
        font-size: 11px;
        color: #666;
        white-space: nowrap;
      }

      /* 提示框样式 */
      .alert {
        padding: 12px;
        border-radius: 8px;
        margin: 10px 0;
      }

      .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      /* 文本颜色 */
      .text-danger {
        color: #dc3545 !important;
      }

      /* 响应式设计 */
      @media (max-width: 1200px) {
        .container {
          max-width: 100%;
          padding: 15px;
        }

        .data-table {
          font-size: 12px;
        }

        .users-table th:nth-child(1),
        .users-table td:nth-child(1) {
          width: 30%;
        }
        .users-table th:nth-child(2),
        .users-table td:nth-child(2) {
          width: 25%;
        }
        .users-table th:nth-child(3),
        .users-table td:nth-child(3) {
          width: 15%;
        }
        .users-table th:nth-child(4),
        .users-table td:nth-child(4) {
          width: 15%;
        }
        .users-table th:nth-child(5),
        .users-table td:nth-child(5) {
          width: 10%;
        }
        .users-table th:nth-child(6),
        .users-table td:nth-child(6) {
          width: 5%;
        }
      }

      @media (max-width: 768px) {
        .tabs {
          flex-wrap: wrap;
        }

        .tab {
          font-size: 14px;
          padding: 8px 16px;
        }

        .data-table {
          font-size: 11px;
        }

        .btn-small {
          font-size: 10px;
          padding: 2px 4px;
          min-width: 30px;
        }

        .code-text,
        .device-id {
          font-size: 9px;
        }

        .status-badge {
          font-size: 9px;
          padding: 2px 4px;
        }
      }

      /* 美化工具提示 */
      [title] {
        position: relative;
      }

      /* 智能Tooltip样式 */
      .smart-tooltip {
        position: fixed;
        background: rgba(15, 23, 42, 0.96);
        color: white;
        padding: 20px 24px;
        border-radius: 12px;
        font-size: 13px;
        line-height: 2.2;
        max-width: 480px;
        min-width: 360px;
        word-wrap: break-word;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        z-index: 99999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(12px);
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        letter-spacing: 0.3px;
        text-align: left;
        pointer-events: none;
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.2s ease;
      }

      .smart-tooltip.show {
        opacity: 1;
        transform: scale(1);
      }

      .smart-tooltip::before {
        content: "";
        position: absolute;
        border: 6px solid transparent;
        border-top-color: #2d3748;
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
      }

      .smart-tooltip[data-placement="bottom"]::before {
        top: -12px;
        bottom: auto;
        border-top-color: transparent;
        border-bottom-color: #2d3748;
      }

      .smart-tooltip[data-placement="left"]::before {
        top: 50%;
        left: auto;
        right: -12px;
        bottom: auto;
        transform: translateY(-50%);
        border-top-color: transparent;
        border-left-color: #2d3748;
      }

      .smart-tooltip[data-placement="right"]::before {
        top: 50%;
        left: -12px;
        bottom: auto;
        transform: translateY(-50%);
        border-top-color: transparent;
        border-right-color: #2d3748;
      }

      @keyframes tooltipFadeIn {
        from {
          opacity: 0;
          transform: translateY(8px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      /* 实时控制面板样式 */
      .control-panel {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .client-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
        margin-top: 25px;
      }

      .client-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .client-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .client-card:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
      }

      .client-card h4 {
        margin-bottom: 12px;
        color: #333;
        font-weight: 700;
        font-size: 16px;
      }

      .client-info {
        font-size: 14px;
        color: #666;
        margin-bottom: 18px;
        line-height: 1.5;
      }

      .command-form {
        display: flex;
        gap: 12px;
        margin-top: 18px;
      }

      .command-input {
        flex: 1;
        padding: 10px 14px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
      }

      .command-input:focus {
        outline: none;
        border-color: #667eea;
      }

      .send-btn {
        padding: 10px 18px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .send-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      .connection-status {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 10px;
        position: relative;
      }

      .connection-status.online {
        background: #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
      }

      .connection-status.online::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        border: 2px solid rgba(40, 167, 69, 0.3);
        animation: pulse 2s infinite;
      }

      .connection-status.offline {
        background: #dc3545;
      }

      /* 广播控制区域 */
      .broadcast-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
      }

      .broadcast-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 100%
        );
        pointer-events: none;
      }

      .broadcast-section h3 {
        margin-bottom: 18px;
        font-weight: 700;
        position: relative;
        z-index: 1;
      }

      .broadcast-form {
        display: flex;
        gap: 15px;
        align-items: flex-end;
        position: relative;
        z-index: 1;
      }

      .broadcast-form .form-group {
        flex: 1;
        margin-bottom: 0;
      }

      .broadcast-form label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
      }

      .broadcast-form input,
      .broadcast-form select {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        transition: all 0.3s ease;
      }

      .broadcast-form input:focus,
      .broadcast-form select:focus {
        border-color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.2);
      }

      .broadcast-form input::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }

      .broadcast-btn {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 14px 28px;
        border-radius: 10px;
        cursor: pointer;
        font-weight: 700;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .broadcast-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .broadcast-btn:hover::before {
        left: 100%;
      }

      .broadcast-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }

      /* 响应式设计 - 极简专业风格 */
      @media (max-width: 1024px) {
        .container {
          max-width: 100%;
          padding: 1.5rem;
        }

        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
          gap: 1rem;
        }
      }

      @media (max-width: 768px) {
        .container {
          padding: 1rem;
        }

        .header {
          margin-bottom: 2rem;
          padding: 1rem 0;
        }

        .header h1 {
          font-size: 1.5rem;
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .tabs {
          flex-direction: column;
          gap: 0.25rem;
        }

        .tab {
          padding: 0.75rem 1rem;
          font-size: 0.875rem;
          text-align: left;
        }

        .login-form,
        .dashboard {
          padding: 2rem;
        }

        .data-table {
          font-size: 0.75rem;
          display: block;
          overflow-x: auto;
          white-space: nowrap;
        }

        .data-table th,
        .data-table td {
          padding: 0.75rem 0.5rem;
        }

        .action-container {
          flex-direction: column;
          gap: 0.25rem;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 0.75rem;
        }

        .header h1 {
          font-size: 1.25rem;
        }

        .login-form,
        .dashboard {
          padding: 1.5rem;
        }

        .stat-card {
          padding: 1.5rem;
        }

        .stat-card h3 {
          font-size: 1.5rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- 连接状态栏 -->
    <div
      id="connectionStatusBar"
      style="
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        text-align: center;
        padding: 8px;
        font-size: 14px;
        font-weight: 600;
        z-index: 9999;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      "
    ></div>

    <div class="container">
      <div class="header">
        <h1>🔐 Augment 激活码管理系统</h1>
        <p>专业的激活码生成与管理平台</p>
        <div style="margin-top: 15px; font-size: 14px; opacity: 0.8">
          <span>💡 快捷键：Ctrl/Cmd + 1-5 切换标签页</span>
        </div>
      </div>

      <!-- 登录表单 -->
      <div id="loginForm" class="login-form animate-slide-in">
        <div style="text-align: center; margin-bottom: 2rem">
          <div
            style="
              width: 4rem;
              height: 4rem;
              background: var(--gradient-primary);
              border-radius: var(--radius-xl);
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 1rem;
            "
          >
            <span style="font-size: 1.5rem">🔐</span>
          </div>
          <h2
            style="
              color: var(--primary-900);
              font-weight: 600;
              margin-bottom: 0.5rem;
            "
          >
            管理员登录
          </h2>
          <p style="color: var(--primary-600); font-size: 0.875rem">
            请输入您的凭据以访问管理系统
          </p>
        </div>
        <div id="loginAlert"></div>
        <form onsubmit="login(event)">
          <div class="form-group">
            <label for="username">用户名</label>
            <input
              type="text"
              id="username"
              required
              placeholder="请输入用户名"
            />
          </div>
          <div class="form-group">
            <label for="password">密码</label>
            <input
              type="password"
              id="password"
              required
              placeholder="请输入密码"
            />
          </div>
          <button type="submit" class="btn" style="width: 100%; padding: 1rem">
            <span>登录系统</span>
          </button>
        </form>
        <div
          style="
            text-align: center;
            margin-top: 1.5rem;
            font-size: 0.75rem;
            color: var(--primary-500);
          "
        >
          默认账户：admin / admin123
        </div>
      </div>

      <!-- 管理面板 -->
      <div id="dashboard" class="dashboard hidden">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
          "
        >
          <h2>管理面板</h2>
          <button onclick="logout()" class="btn btn-secondary">退出登录</button>
        </div>

        <!-- 统计信息 -->
        <div id="statsGrid" class="stats-grid">
          <!-- 统计卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 标签页 -->
        <div class="tabs">
          <button class="tab active" onclick="showTab('generate')">
            生成激活码
          </button>
          <button class="tab" onclick="showTab('codes')">激活码列表</button>
          <button class="tab" onclick="showTab('logs')">使用记录</button>
          <button class="tab" onclick="showTab('users')">用户管理</button>
          <button class="tab" onclick="showTab('control')">实时控制</button>
        </div>

        <!-- 生成激活码 -->
        <div id="generateTab" class="tab-content active">
          <h3>生成新激活码</h3>
          <div id="generateAlert"></div>
          <form onsubmit="generateCode(event)">
            <div
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px"
            >
              <div class="form-group">
                <label for="deviceId">设备ID (可选)</label>
                <input
                  type="text"
                  id="deviceId"
                  placeholder="留空表示任意设备可用"
                />
              </div>
              <div class="form-group">
                <label for="expiryDays">有效期 (天)</label>
                <select id="expiryDays">
                  <option value="7">7天</option>
                  <option value="30" selected>30天</option>
                  <option value="90">90天</option>
                  <option value="365">365天</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="notes">备注</label>
              <textarea
                id="notes"
                name="notes"
                rows="3"
                placeholder="可选的备注信息"
              ></textarea>
            </div>
            <button type="submit" class="btn">生成激活码</button>
          </form>
          <div id="generatedCode"></div>
        </div>

        <!-- 激活码列表 -->
        <div id="codesTab" class="tab-content">
          <h3>激活码列表</h3>
          <button onclick="loadCodes()" class="btn" style="margin-bottom: 20px">
            刷新列表
          </button>
          <div id="codesTable"></div>
        </div>

        <!-- 使用记录 -->
        <div id="logsTab" class="tab-content">
          <h3>使用记录</h3>
          <button onclick="loadLogs()" class="btn" style="margin-bottom: 20px">
            刷新记录
          </button>
          <div id="logsTable"></div>
        </div>

        <!-- 用户管理 -->
        <div id="usersTab" class="tab-content">
          <h3>用户管理</h3>
          <button onclick="loadUsers()" class="btn" style="margin-bottom: 20px">
            刷新用户
          </button>
          <div id="usersTable"></div>
        </div>

        <!-- 实时控制 -->
        <div id="controlTab" class="tab-content">
          <h3>实时控制</h3>
          <div style="margin-bottom: 20px">
            <button onclick="loadConnectedClients()" class="btn">
              刷新客户端
            </button>
            <button onclick="broadcastMessage()" class="btn btn-secondary">
              广播消息
            </button>
          </div>
          <div id="connectedClientsTable"></div>
        </div>
      </div>
    </div>

    <script src="app.js"></script>
    <script>
      // 智能Tooltip系统
      class SmartTooltip {
        constructor() {
          this.tooltip = null;
          this.currentTarget = null;
          this.showTimeout = null;
          this.hideTimeout = null;
          this.init();
        }

        init() {
          // 创建tooltip元素
          this.tooltip = document.createElement("div");
          this.tooltip.className = "smart-tooltip";
          document.body.appendChild(this.tooltip);

          // 绑定事件
          this.bindEvents();
        }

        bindEvents() {
          document.addEventListener("mouseover", (e) => {
            const target = e.target.closest("[title]");
            if (target && target.title && this.shouldShowTooltip(target)) {
              this.show(target, target.title);
            }
          });

          document.addEventListener("mouseout", (e) => {
            const target = e.target.closest("[title]");
            if (target === this.currentTarget) {
              this.hide();
            }
          });

          document.addEventListener("scroll", () => {
            if (this.currentTarget) {
              this.updatePosition();
            }
          });

          window.addEventListener("resize", () => {
            if (this.currentTarget) {
              this.updatePosition();
            }
          });
        }

        shouldShowTooltip(element) {
          // 检查是否需要显示tooltip
          const title = element.title.trim();
          if (!title) return false;

          // 检查文本是否溢出或被截断
          const textContent = element.textContent.trim();

          // 对于代码元素，特殊处理
          if (
            element.classList.contains("code-text") ||
            element.classList.contains("device-id")
          ) {
            // 只有当title和显示文本不同时才显示（说明文本被截断了）
            return title !== textContent;
          }

          // 对于其他元素，检查是否有溢出
          if (title === textContent) {
            // 检查元素是否有文本溢出
            const hasOverflow =
              element.scrollWidth > element.clientWidth ||
              element.scrollHeight > element.clientHeight;
            return hasOverflow;
          }

          // 如果title和显示文本不同，说明有额外信息需要显示
          return true;
        }

        show(target, text) {
          if (this.showTimeout) clearTimeout(this.showTimeout);
          if (this.hideTimeout) clearTimeout(this.hideTimeout);

          this.currentTarget = target;
          this.tooltip.textContent = text;

          this.showTimeout = setTimeout(() => {
            this.updatePosition();
            this.tooltip.classList.add("show");
          }, 300);
        }

        hide() {
          if (this.showTimeout) clearTimeout(this.showTimeout);

          this.hideTimeout = setTimeout(() => {
            this.tooltip.classList.remove("show");
            this.currentTarget = null;
          }, 100);
        }

        updatePosition() {
          if (!this.currentTarget) return;

          const targetRect = this.currentTarget.getBoundingClientRect();
          const tooltipRect = this.tooltip.getBoundingClientRect();
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          let x, y;
          let placement = "top"; // 默认在上方

          // 计算最佳位置
          const positions = {
            top: {
              x: targetRect.left + targetRect.width / 2 - tooltipRect.width / 2,
              y: targetRect.top - tooltipRect.height - 12,
            },
            bottom: {
              x: targetRect.left + targetRect.width / 2 - tooltipRect.width / 2,
              y: targetRect.bottom + 12,
            },
            left: {
              x: targetRect.left - tooltipRect.width - 12,
              y:
                targetRect.top + targetRect.height / 2 - tooltipRect.height / 2,
            },
            right: {
              x: targetRect.right + 12,
              y:
                targetRect.top + targetRect.height / 2 - tooltipRect.height / 2,
            },
          };

          // 选择最佳位置（优先级：top > bottom > right > left）
          for (const pos of ["top", "bottom", "right", "left"]) {
            const position = positions[pos];
            if (
              position.x >= 8 &&
              position.x + tooltipRect.width <= viewportWidth - 8 &&
              position.y >= 8 &&
              position.y + tooltipRect.height <= viewportHeight - 8
            ) {
              x = position.x;
              y = position.y;
              placement = pos;
              break;
            }
          }

          // 如果没有合适位置，使用智能调整
          if (x === undefined || y === undefined) {
            x = Math.min(
              Math.max(
                8,
                targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
              ),
              viewportWidth - tooltipRect.width - 8
            );
            y = Math.min(
              Math.max(8, targetRect.top - tooltipRect.height - 12),
              viewportHeight - tooltipRect.height - 8
            );
          }

          this.tooltip.style.left = x + scrollX + "px";
          this.tooltip.style.top = y + scrollY + "px";
          this.tooltip.setAttribute("data-placement", placement);
        }
      }

      // 初始化智能Tooltip
      document.addEventListener("DOMContentLoaded", () => {
        new SmartTooltip();
      });
    </script>
  </body>
</html>
