<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment设备管理器</title>
    <script src="tailwind.css"></script>
    <!-- 优先加载renderer.js，确保函数定义不被覆盖 -->
    <script src="renderer.js"></script>
    <style>
      /* 极简风格样式 */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(8px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateX(-12px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.8;
          transform: scale(1.05);
        }
      }

      @keyframes deviceIdUpdate {
        0% {
          background-color: #dcfce7;
          border-color: #16a34a;
          transform: scale(1);
        }
        50% {
          background-color: #bbf7d0;
          border-color: #15803d;
          transform: scale(1.02);
        }
        100% {
          background-color: #dcfce7;
          border-color: #16a34a;
          transform: scale(1);
        }
      }

      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.4s ease-out;
      }
      .animate-slideIn {
        animation: slideIn 0.3s ease-out;
      }
      .animate-pulse {
        animation: pulse 2s infinite;
      }
      .animate-spin {
        animation: spin 1s linear infinite;
      }

      /* 卡片样式 */
      .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
      }

      .card:hover {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      }

      /* 标签页 */
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease-out;
      }

      /* 极简背景 */
      .minimal-bg {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      }

      /* 状态指示器 */
      .status-dot {
        position: relative;
      }

      .status-dot::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: inherit;
        animation: pulse 2s infinite;
        opacity: 0.4;
      }

      /* 滚动条 */
      ::-webkit-scrollbar {
        width: 4px;
      }
      ::-webkit-scrollbar-track {
        background: #f1f5f9;
      }
      ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
      }

      /* 优化的Tooltip样式 */
      [data-tooltip] {
        position: relative;
      }

      [data-tooltip]:hover::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 8px;
        padding: 20px 24px;
        background: rgba(15, 23, 42, 0.96);
        color: white;
        font-size: 13px;
        line-height: 2.2;
        border-radius: 12px;
        white-space: pre-wrap;
        word-wrap: break-word;
        word-break: normal;
        overflow-wrap: break-word;
        z-index: 1000;
        max-width: 480px;
        min-width: 360px;
        width: max-content;
        text-align: left;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        animation: fadeIn 0.3s ease-out;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        letter-spacing: 0.3px;
        hyphens: auto;
      }

      [data-tooltip]:hover::after {
        content: "";
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 2px;
        border: 6px solid transparent;
        border-top-color: rgba(30, 41, 59, 0.95);
        z-index: 1001;
        pointer-events: none;
        animation: fadeIn 0.2s ease-out;
      }

      /* 针对右侧元素的tooltip位置调整 */
      .tooltip-left[data-tooltip]:hover::before {
        left: auto;
        right: 0;
        transform: none;
      }

      .tooltip-left[data-tooltip]:hover::after {
        left: auto;
        right: 12px;
        transform: none;
      }

      /* 加载状态 */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(248, 250, 252, 0.9);
        backdrop-filter: blur(4px);
        z-index: 9999;
      }

      /* Storage保护相关样式 */
      .storage-protection-section {
        margin: 1.5rem 0;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 1rem;
        border: 1px solid rgba(226, 232, 240, 0.5);
      }

      .protection-status {
        margin: 1rem 0;
        padding: 1rem;
        border-radius: 0.75rem;
        border: 2px solid;
        transition: all 0.3s ease;
      }

      .protection-status.protected {
        background-color: #dcfce7;
        border-color: #16a34a;
        color: #15803d;
      }

      .protection-status.unprotected {
        background-color: #fef3c7;
        border-color: #f59e0b;
        color: #d97706;
      }

      .protection-status.error {
        background-color: #fee2e2;
        border-color: #dc2626;
        color: #b91c1c;
      }

      .protection-status.checking {
        background-color: #e0f2fe;
        border-color: #0284c7;
        color: #0369a1;
      }

      .protection-status.processing {
        background-color: #f3e8ff;
        border-color: #9333ea;
        color: #7c3aed;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
      }

      .protection-controls {
        display: flex;
        gap: 0.75rem;
        margin: 1rem 0;
        flex-wrap: wrap;
      }

      .protection-info {
        margin: 1rem 0;
        padding: 1rem;
        background-color: #f8fafc;
        border-radius: 0.5rem;
        border-left: 4px solid #64748b;
      }

      .protection-info p {
        margin: 0.25rem 0;
        color: #475569;
        font-size: 0.875rem;
      }

      .btn-primary,
      .btn-secondary,
      .btn-outline {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
      }

      .btn-primary {
        background-color: #16a34a;
        color: white;
      }

      .btn-primary:hover {
        background-color: #15803d;
      }

      .btn-secondary {
        background-color: #f59e0b;
        color: white;
      }

      .btn-secondary:hover {
        background-color: #d97706;
      }

      .btn-outline {
        background-color: transparent;
        color: #64748b;
        border: 1px solid #cbd5e1;
      }

      .btn-outline:hover {
        background-color: #f1f5f9;
        border-color: #94a3b8;
      }

      .results-section {
        margin: 1rem 0;
        padding: 1rem;
        background-color: #f8fafc;
        border-radius: 0.5rem;
        border: 1px solid #e2e8f0;
      }

      .results-section p {
        margin: 0.25rem 0;
        font-size: 0.875rem;
        color: #475569;
      }
    </style>
  </head>
  <body class="min-h-screen minimal-bg">
    <!-- 加载状态 -->
    <div
      id="loading"
      class="loading-overlay hidden items-center justify-center"
    >
      <div class="text-center">
        <div
          class="w-8 h-8 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin mx-auto mb-3"
        ></div>
        <p class="text-slate-600 text-sm">加载中...</p>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="container mx-auto px-6 py-8 max-w-6xl">
      <!-- 头部 -->
      <header class="text-center mb-12 animate-fadeIn relative">
        <!-- 右上角按钮组 -->
        <div class="absolute top-0 right-0 flex gap-3">
          <!-- 公告历史按钮 -->
          <button
            onclick="toggleAnnouncementHistory()"
            class="group relative p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-white/90"
            title="查看公告历史"
          >
            <svg
              class="w-5 h-5 text-slate-600 group-hover:text-slate-800 transition-colors duration-300"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
              />
              <path
                d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
              />
            </svg>
            <!-- 公告计数徽章 -->
            <span
              id="announcement-badge"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium hidden"
            >
              0
            </span>
          </button>

          <!-- 主题切换按钮 -->
          <button
            id="theme-toggle-btn"
            onclick="toggleTheme()"
            class="group relative p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-white/90"
            title="切换到丰富风格"
          >
            <svg
              class="w-5 h-5 text-slate-600 group-hover:text-slate-800 transition-colors duration-300"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M4 2a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V4a1 1 0 100-2H4zm2 3a1 1 0 000 2h8a1 1 0 100-2H6zM6 9a1 1 0 100 2h8a1 1 0 100-2H6zM6 13a1 1 0 100 2h5a1 1 0 100-2H6z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <!-- 工具提示 -->
            <div
              class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-slate-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
            >
              切换到丰富风格
              <div
                class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-800"
              ></div>
            </div>
          </button>
        </div>

        <div class="flex items-center justify-center gap-3 mb-4">
          <div
            class="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-2xl flex items-center justify-center"
          >
            <svg
              class="w-6 h-6 text-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
              />
            </svg>
          </div>
          <h1 class="text-3xl font-light text-slate-800">Augment 设备管理器</h1>
        </div>
        <p class="text-slate-500 text-lg font-light">
          Cursor IDE 设备限制解决方案
        </p>

        <!-- 状态指示器 -->
        <div
          class="inline-flex items-center gap-2 mt-6 px-4 py-2 bg-white/80 rounded-full shadow-sm"
        >
          <div
            id="quick-status-indicator"
            class="w-2 h-2 bg-red-500 rounded-full status-dot"
          ></div>
          <span id="quick-status-text" class="text-sm text-slate-600"
            >设备未激活</span
          >
        </div>
      </header>

      <!-- 导航标签 -->
      <nav class="flex justify-center mb-8 animate-slideIn">
        <div class="flex bg-white/80 rounded-2xl p-1 shadow-sm">
          <button
            id="tab-btn-dashboard"
            onclick="switchTab('dashboard')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 bg-slate-100 text-slate-800"
          >
            仪表盘
          </button>
          <button
            id="tab-btn-activation"
            onclick="switchTab('activation')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            设备激活
          </button>
          <button
            id="tab-btn-tools"
            onclick="switchTab('tools')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            工具
          </button>
          <button
            id="tab-btn-system"
            onclick="switchTab('system')"
            class="tab-btn px-6 py-3 text-sm font-medium rounded-xl transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
          >
            系统
          </button>
        </div>
      </nav>

      <!-- 仪表盘标签页 -->
      <div id="dashboard-tab" class="tab-content active">
        <!-- 状态卡片网格 -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
          <!-- 设备状态 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">设备状态</h3>
                <p class="text-sm text-slate-500">激活状态</p>
              </div>
            </div>
            <div class="text-center">
              <h4
                id="status-text"
                class="text-xl font-semibold text-red-600 mb-1"
              >
                未激活
              </h4>
              <p id="status-detail" class="text-sm text-slate-500">
                请输入激活码以启用设备功能
              </p>
            </div>
          </div>

          <!-- 连接状态 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">连接状态</h3>
                <p class="text-sm text-slate-500">服务器连接</p>
              </div>
            </div>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">服务器</span>
                <div id="connection-status" class="flex items-center gap-2">
                  <div class="w-2 h-2 bg-red-500 rounded-full status-dot"></div>
                  <span class="text-sm font-medium text-red-600">未连接</span>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">延迟</span>
                <span
                  id="network-latency"
                  class="text-sm font-medium text-slate-800"
                  >-</span
                >
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-slate-600">最后同步</span>
                <span id="last-sync" class="text-sm text-slate-500">从未</span>
              </div>
            </div>
            <button
              onclick="testServerConnection()"
              class="w-full mt-4 px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors"
            >
              测试连接
            </button>
          </div>

          <!-- 系统信息 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">系统信息</h3>
                <p class="text-sm text-slate-500">性能监控</p>
              </div>
            </div>
            <div class="space-y-3">
              <div>
                <div class="flex justify-between items-center mb-1">
                  <span class="text-sm text-slate-600">CPU</span>
                  <span id="cpu-text" class="text-sm font-medium">0%</span>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2">
                  <div
                    id="cpu-progress"
                    class="bg-gradient-to-r from-emerald-400 to-emerald-500 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between items-center mb-1">
                  <span class="text-sm text-slate-600">内存</span>
                  <span id="memory-text" class="text-sm font-medium">0%</span>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2">
                  <div
                    id="memory-progress"
                    class="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 设备激活标签页 -->
      <div id="activation-tab" class="tab-content">
        <div class="max-w-md mx-auto">
          <div class="card rounded-2xl p-8 shadow-sm text-center">
            <div
              class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <svg
                class="w-8 h-8 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                />
              </svg>
            </div>

            <!-- 激活表单 -->
            <div id="activation-form">
              <h3 class="text-xl font-semibold text-slate-800 mb-2">
                设备激活
              </h3>
              <p class="text-slate-500 mb-6">请输入激活码以启用设备功能</p>

              <div class="space-y-4">
                <input
                  type="text"
                  id="activation-code"
                  placeholder="请输入激活码"
                  class="w-full px-4 py-3 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
                <button
                  onclick="validateActivation()"
                  class="w-full px-6 py-3 bg-emerald-500 text-white font-medium rounded-xl hover:bg-emerald-600 transition-colors"
                >
                  激活设备
                </button>
              </div>
            </div>

            <!-- 已激活信息 -->
            <div id="activated-info" class="hidden">
              <h3 class="text-xl font-semibold text-emerald-600 mb-2">
                设备已激活
              </h3>
              <p class="text-slate-500 mb-4">设备功能已启用</p>
              <div
                id="activation-details"
                class="text-sm text-slate-600 bg-slate-50 rounded-lg p-4 mb-4"
              ></div>
              <button
                onclick="deactivateDevice()"
                class="w-full px-6 py-3 bg-red-500 text-white font-medium rounded-xl hover:bg-red-600 transition-colors"
              >
                退出激活
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具标签页 -->
      <div id="tools-tab" class="tab-content">
        <!-- 主要布局：左侧清理工具，右侧其他工具 -->
        <div class="grid md:grid-cols-2 gap-6">
          <!-- 左侧：清理工具 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-4">
              <div
                class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h6a1 1 0 100-2H7z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">清理工具</h3>
                <p class="text-sm text-slate-500">清理系统缓存</p>
              </div>
            </div>
            <p class="text-sm text-slate-600 mb-4">
              清理IDE相关的缓存文件和临时数据，释放磁盘空间。
            </p>

            <!-- 功能说明 -->
            <div
              class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg"
            >
              <h3
                class="text-sm font-medium text-amber-800 mb-2 flex items-center gap-2"
              >
                💡 功能说明
              </h3>
              <div class="text-xs text-amber-700 space-y-1">
                <p>
                  <strong>默认清理</strong>：仅清理 Augment
                  扩展数据，让扩展认为是新设备，保留 IDE 登录状态
                </p>
                <p>
                  <strong>完全重置</strong>：清理所有 IDE 数据，让 IDE
                  也认为是全新用户，需要重新登录
                </p>
              </div>
            </div>

            <!-- 清理模式选择区域 -->
            <div
              class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg"
            >
              <h3 class="text-lg font-semibold text-blue-800 mb-3">
                🎯 选择清理模式
              </h3>
              <div class="space-y-3">
                <label
                  class="flex items-start gap-3 text-sm cursor-pointer p-3 bg-white rounded-lg border border-green-200 hover:border-green-300 transition-colors"
                >
                  <input
                    type="radio"
                    name="cleanup-mode"
                    value="intelligent"
                    checked
                    class="mt-1 text-green-600 focus:ring-green-500"
                  />
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <span class="text-slate-800 font-medium"
                        >🧠 智能清理</span
                      >
                      <span
                        class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                        >推荐</span
                      >
                    </div>
                    <p class="text-xs text-slate-600 mt-1">
                      只清理设备身份数据，保留所有用户配置和设置。无风险，适合日常使用。
                    </p>
                  </div>
                </label>

                <label
                  class="flex items-start gap-3 text-sm cursor-pointer p-3 bg-white rounded-lg border border-orange-200 hover:border-orange-300 transition-colors"
                >
                  <input
                    type="radio"
                    name="cleanup-mode"
                    value="standard"
                    class="mt-1 text-orange-600 focus:ring-orange-500"
                  />
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <span class="text-slate-800 font-medium"
                        >🔧 标准清理</span
                      >
                      <span
                        class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full"
                        >中等风险</span
                      >
                    </div>
                    <p class="text-xs text-slate-600 mt-1">
                      清理大部分IDE数据，保留核心配置。需要重新配置部分设置。
                    </p>
                  </div>
                </label>

                <label
                  class="flex items-start gap-3 text-sm cursor-pointer p-3 bg-white rounded-lg border border-red-200 hover:border-red-300 transition-colors"
                >
                  <input
                    type="radio"
                    name="cleanup-mode"
                    value="complete"
                    class="mt-1 text-red-600 focus:ring-red-500"
                  />
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <span class="text-slate-800 font-medium"
                        >💥 完全清理</span
                      >
                      <span
                        class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full"
                        >高风险</span
                      >
                    </div>
                    <p class="text-xs text-slate-600 mt-1">
                      彻底重置IDE，回到全新安装状态。仅保护MCP配置。
                    </p>
                  </div>
                </label>
              </div>
            </div>

            <!-- IDE选择区域 -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 class="text-lg font-semibold text-blue-800 mb-3">
                🎯 选择要清理和防护的IDE
              </h3>
              <div class="space-y-2">
                <label class="flex items-center gap-3 text-sm cursor-pointer">
                  <input
                    type="radio"
                    name="ide-selection"
                    id="clean-cursor"
                    value="cursor"
                    checked
                    class="border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="text-slate-800">🎨 Cursor IDE</span>
                  <span class="text-xs text-slate-500 ml-2"
                    >(清理Augment扩展数据并启用增强防护)</span
                  >
                </label>
                <label class="flex items-center gap-3 text-sm cursor-pointer">
                  <input
                    type="radio"
                    name="ide-selection"
                    id="clean-vscode"
                    value="vscode"
                    class="border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="text-slate-800">💙 Visual Studio Code</span>
                  <span class="text-xs text-slate-500 ml-2"
                    >(清理Augment扩展数据并启用增强防护)</span
                  >
                </label>
              </div>
            </div>

            <!-- PowerShell辅助选项 - 隐藏，由清理模式自动控制 -->
            <div class="mb-4" style="display: none">
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="use-powershell-assist"
                  class="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                  checked
                />
                <span class="text-slate-800">🚀 启用PowerShell辅助清理</span>
              </label>
            </div>

            <!-- 增强防护选项 - 对用户可见 -->
            <div class="mb-4 space-y-2" style="display: none">
              <h3 class="text-lg font-semibold text-slate-800 mb-3">
                🛡️ 增强防护选项
              </h3>
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="skip-backup"
                  checked
                  class="rounded border-slate-300"
                />
                <span class="text-slate-700"
                  >🚫 跳过备份文件创建（防止IDE恢复）</span
                >
              </label>
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="enable-enhanced-guardian"
                  checked
                  class="rounded border-slate-300"
                />
                <span class="text-slate-700"
                  >🛡️ 启用增强守护进程（实时防护）</span
                >
              </label>

              <!-- 增强防护说明 -->
              <div
                class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <div class="flex items-start gap-2">
                  <svg
                    class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <div class="text-xs text-blue-700">
                    <div class="font-medium mb-1">🔒 持续防护说明</div>
                    <div class="space-y-1">
                      <div>
                        • <strong>独立服务模式</strong>：客户端关闭后继续运行
                      </div>
                      <div>
                        • <strong>24/7监控</strong>：实时保护storage.json文件
                      </div>
                      <div>
                        • <strong>自动拦截</strong>：阻止Cursor恢复旧设备ID
                      </div>
                      <div>
                        • <strong>手动停止</strong>：通过下方状态卡片停止防护
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 传统清理选项 - 隐藏但保持默认选中状态 -->
            <div class="mb-4 space-y-2" style="display: none">
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="preserve-activation"
                  checked
                  class="rounded border-slate-300"
                />
                <span class="text-slate-700">保留激活状态（推荐）</span>
              </label>
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="deep-clean"
                  checked
                  class="rounded border-slate-300"
                />
                <span class="text-slate-700">深度清理（可能影响设备ID）</span>
              </label>
              <label class="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  id="clean-cursor-extension"
                  checked
                  class="rounded border-slate-300"
                />
                <span class="text-slate-700"
                  >🎯 清理Cursor IDE扩展数据（让Augment扩展认为是新设备）</span
                >
              </label>
            </div>

            <!-- 重置选项 - 隐藏，由清理模式自动控制 -->
            <div class="mb-4" style="display: none">
              <input type="checkbox" id="reset-cursor-completely" />
              <input type="checkbox" id="reset-vscode-completely" />
            </div>

            <div class="mb-4">
              <button
                onclick="performCleanupAndStartGuardian()"
                class="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-medium rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 flex items-center justify-center gap-2"
              >
                <span>🧹</span>
                <span>清理设备</span>
                <span>🛡️</span>
              </button>
              <p class="text-xs text-gray-500 mt-2 text-center">
                清理完成后将自动启动对应IDE的防护服务
              </p>
            </div>

            <!-- 增强防护控制已移至右上角 -->

            <!-- 清理日志显示区域 -->
            <div id="cleanup-log-container" class="hidden">
              <div class="bg-slate-50 rounded-lg p-3 border">
                <div class="flex items-center justify-between mb-2">
                  <h4 class="text-sm font-medium text-slate-700">清理日志</h4>
                </div>
                <div
                  id="cleanup-log"
                  class="text-xs text-slate-600 bg-white rounded border p-2 max-h-48 overflow-y-auto font-mono"
                >
                  等待清理操作...
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：其他工具垂直排列 -->
          <div class="space-y-6">
            <!-- 增强防护状态 - 重构版 -->
            <div
              class="bg-white rounded-2xl p-6 shadow-lg border border-slate-100"
            >
              <!-- 标题区域 -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center gap-3">
                  <div
                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"
                  >
                    <svg
                      class="w-6 h-6 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-slate-800">
                      增强防护
                    </h3>
                    <p class="text-sm text-slate-500">实时监控防护状态</p>
                  </div>
                </div>

                <!-- 状态指示器 -->
                <div class="flex items-center gap-2">
                  <div
                    id="guardian-status-indicator"
                    class="w-3 h-3 bg-slate-400 rounded-full animate-pulse"
                  ></div>
                  <span
                    id="guardian-status"
                    class="text-sm font-medium text-slate-600"
                    >未启动</span
                  >
                </div>
              </div>

              <!-- 统计数据卡片 -->
              <div class="grid grid-cols-3 gap-4 mb-6">
                <div
                  class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 border border-red-200/50"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <div
                        id="intercept-count"
                        class="text-2xl font-bold text-red-700"
                      >
                        0
                      </div>
                      <div class="text-xs font-medium text-red-600">
                        拦截次数
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200/50"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"
                          clip-rule="evenodd"
                        />
                        <path
                          fill-rule="evenodd"
                          d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM8 8a1 1 0 012 0v3a1 1 0 11-2 0V8zm4 0a1 1 0 10-2 0v3a1 1 0 102 0V8z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <div
                        id="backup-removed"
                        class="text-2xl font-bold text-orange-700"
                      >
                        0
                      </div>
                      <div class="text-xs font-medium text-orange-600">
                        删除备份
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200/50"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <div
                        id="protection-restored"
                        class="text-2xl font-bold text-blue-700"
                      >
                        0
                      </div>
                      <div class="text-xs font-medium text-blue-600">
                        恢复保护
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 防护性能信息 -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div
                  class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200/50"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <div
                        id="guardian-uptime"
                        class="text-2xl font-bold text-green-700"
                      >
                        0分钟
                      </div>
                      <div class="text-xs font-medium text-green-600">
                        防护运行时间
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200/50"
                >
                  <div class="flex items-center gap-3">
                    <div
                      class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center"
                    >
                      <svg
                        class="w-5 h-5 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div
                        id="guardian-memory"
                        class="text-2xl font-bold text-purple-700"
                      >
                        0MB
                      </div>
                      <div class="text-xs font-medium text-purple-600">
                        内存占用
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 设备ID显示区域 -->
              <div
                class="mb-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl"
              >
                <div class="text-center">
                  <p
                    class="text-slate-600 mb-3 font-medium flex items-center justify-center gap-2"
                  >
                    <svg
                      class="w-4 h-4 text-blue-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                      />
                      <path
                        d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"
                      />
                    </svg>
                    设备ID（清理成功标志）
                  </p>
                  <div class="flex items-center justify-center gap-2">
                    <p
                      id="device-id-text"
                      class="font-mono text-sm text-slate-800 bg-white border border-blue-300 px-4 py-2 rounded-lg font-semibold select-all shadow-sm"
                      style="word-break: break-all; max-width: 300px"
                    >
                      获取中...
                    </p>
                    <button
                      onclick="copyDeviceId()"
                      class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-colors"
                      title="复制设备ID"
                    >
                      <svg
                        class="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                        />
                        <path
                          d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 控制按钮区域 -->
              <div class="flex gap-3">
                <button
                  id="start-guardian-btn"
                  class="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                  title="启动增强防护服务"
                >
                  启动防护
                </button>

                <button
                  id="stop-all-node-btn"
                  onclick="stopAllNodeProcesses()"
                  class="px-2 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white font-medium rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 flex items-center justify-center gap-1 shadow-lg hover:shadow-xl"
                  title="停止所有Node.js进程"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 10-2 0v4a1 1 0 102 0V7z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-xs">停止Node</span>
                </button>

                <button
                  id="refresh-guardian-btn"
                  onclick="window.refreshGuardianStatus && window.refreshGuardianStatus('manual')"
                  class="px-4 py-3 bg-slate-100 text-slate-700 font-medium rounded-xl hover:bg-slate-200 transition-all duration-200 flex items-center justify-center shadow-md hover:shadow-lg"
                  title="手动刷新防护状态和统计数据"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <!-- 扩展信息 -->
            <div class="card rounded-2xl p-6 shadow-sm">
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">扩展信息</h3>
                  <p class="text-sm text-slate-500">Augment扩展</p>
                </div>
              </div>
              <div id="augment-info" class="text-sm text-slate-600 mb-4">
                <p>正在获取扩展信息...</p>
              </div>
              <button
                onclick="refreshAllClientData()"
                class="w-full px-4 py-2 bg-cyan-500 text-white font-medium rounded-lg hover:bg-cyan-600 transition-colors"
                title="刷新整个客户端数据"
              >
                刷新全部数据
              </button>
            </div>
            <!-- 更新检查 -->
            <div class="card rounded-2xl p-6 shadow-sm">
              <div class="flex items-center gap-3 mb-4">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center"
                >
                  <svg
                    class="w-5 h-5 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-slate-800">检查更新</h3>
                  <p class="text-sm text-slate-500">应用程序更新</p>
                </div>
              </div>
              <p class="text-sm text-slate-600 mb-4">
                检查并安装最新版本的设备管理器。
              </p>
              <button
                onclick="checkForUpdates()"
                class="w-full px-4 py-2 bg-indigo-500 text-white font-medium rounded-lg hover:bg-indigo-600 transition-colors"
              >
                检查更新
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统标签页 -->
      <div id="system-tab" class="tab-content">
        <div class="grid lg:grid-cols-2 gap-6">
          <!-- 系统性能 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                  />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-slate-800">系统性能</h3>
                <p class="text-sm text-slate-500">实时监控</p>
              </div>
            </div>

            <!-- 性能指标 -->
            <div class="space-y-6">
              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >CPU使用率</span
                  >
                  <span
                    id="cpu-text-detail"
                    class="text-sm font-semibold text-slate-900"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="cpu-progress-detail"
                    class="bg-gradient-to-r from-emerald-400 to-emerald-500 h-3 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>

              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >内存使用率</span
                  >
                  <span
                    id="memory-text-detail"
                    class="text-sm font-semibold text-slate-900"
                    >0%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="memory-progress-detail"
                    class="bg-gradient-to-r from-blue-400 to-blue-500 h-3 rounded-full transition-all duration-500"
                    style="width: 0%"
                  ></div>
                </div>
              </div>

              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-slate-700"
                    >磁盘使用率</span
                  >
                  <span
                    id="disk-text"
                    class="text-sm font-semibold text-slate-900"
                    >57%</span
                  >
                </div>
                <div class="w-full bg-slate-200 rounded-full h-3">
                  <div
                    id="disk-progress"
                    class="bg-gradient-to-r from-purple-400 to-purple-500 h-3 rounded-full transition-all duration-500"
                    style="width: 57%"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 系统详情 -->
            <div class="mt-6 pt-6 border-t border-slate-200">
              <div class="grid grid-cols-3 gap-4 text-sm">
                <div class="text-center">
                  <p class="text-slate-500 mb-1">主机名</p>
                  <p id="hostname-text" class="font-medium text-slate-800">
                    ZK
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-slate-500 mb-1">系统运行时间</p>
                  <p id="uptime-text-system" class="font-medium text-slate-800">
                    20小时42分钟
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-slate-500 mb-1">软件运行时间</p>
                  <p id="app-uptime-text" class="font-medium text-slate-800">
                    2小时15分钟
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-slate-500 mb-1">CPU核心</p>
                  <p id="cpu-cores-text" class="font-medium text-slate-800">
                    12核
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-slate-500 mb-1">总内存</p>
                  <p
                    id="total-memory-text-system"
                    class="font-medium text-slate-800"
                  >
                    15.81GB
                  </p>
                </div>
              </div>

              <!-- 设备ID -->
              <div class="mt-4 pt-4 border-t border-slate-200">
                <div class="text-center">
                  <!-- 详细设备ID信息展示 -->
                  <div class="mt-6 p-4 bg-gray-50 rounded-lg border">
                    <h4
                      class="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2"
                    >
                      <svg
                        class="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                        />
                      </svg>
                      设备标识符详情
                    </h4>

                    <!-- 稳定设备ID -->
                    <div class="mb-3">
                      <div class="flex justify-between items-center mb-1">
                        <div class="flex items-center gap-1">
                          <span class="text-xs font-medium text-gray-600"
                            >稳定设备ID</span
                          >
                          <svg
                            class="w-3 h-3 text-gray-400 cursor-help"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            data-tooltip="⚙️ 生成方式
基于真实硬件信息生成 SHA256 哈希

📊 数据来源
• os.platform() - 操作系统平台
• os.arch() - 系统架构
• os.hostname() - 主机名
• os.cpus() - CPU信息
• os.totalmem() - 内存大小
• os.userInfo().username - 用户名
• os.networkInterfaces() - 网络接口MAC地址

🎯 用途
设备识别，确保同一设备ID稳定

🧹 清理效果
删除缓存后重新生成新的设备ID"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                        <span
                          id="stable-id-cleanable"
                          class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800"
                          >可清理</span
                        >
                      </div>
                      <div
                        class="font-mono text-xs text-gray-800 bg-white border px-2 py-1 rounded break-all cursor-pointer hover:bg-gray-50"
                        title="点击复制完整设备ID"
                      >
                        <span id="stable-device-id">获取中...</span>
                      </div>
                    </div>

                    <!-- 设备指纹 -->
                    <div class="mb-3">
                      <div class="flex justify-between items-center mb-1">
                        <div class="flex items-center gap-1">
                          <span class="text-xs font-medium text-gray-600"
                            >设备指纹</span
                          >
                          <svg
                            class="w-3 h-3 text-gray-400 cursor-help"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            data-tooltip="🔍 生成方式
高级设备检测生成的指纹

📊 数据来源
• 基础硬件信息（CPU、内存、系统架构）
• 详细系统特征和环境信息
• 网络接口和硬件标识符
• 操作系统版本和配置信息
• 虚拟机检测结果

🎯 用途
比稳定设备ID包含更多检测点
用于精确设备识别

🧹 清理效果
重新收集硬件信息生成新指纹"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                        <span
                          id="fingerprint-cleanable"
                          class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800"
                          >可清理</span
                        >
                      </div>
                      <div
                        class="font-mono text-xs text-gray-800 bg-white border px-2 py-1 rounded break-all cursor-pointer hover:bg-gray-50"
                        title="点击复制完整设备指纹"
                      >
                        <span id="device-fingerprint">获取中...</span>
                      </div>
                    </div>

                    <!-- 设备缓存状态 -->
                    <div class="mb-3">
                      <div class="flex justify-between items-center mb-1">
                        <div class="flex items-center gap-1">
                          <span class="text-xs font-medium text-gray-600"
                            >设备缓存</span
                          >
                          <svg
                            class="w-3 h-3 text-gray-400 cursor-help"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            data-tooltip="💾 缓存机制
设备ID的本地缓存状态

📍 存储位置
• 主缓存：C:\Users\<USER>\.augment-device-manager\stable-device-id.cache
• 备份缓存：C:\Users\<USER>\.augment-device-manager\stable-device-id.backup

🎯 作用
确保设备ID在重启后保持稳定
避免频繁重新生成

🧹 清理效果
删除所有缓存文件
下次启动时重新生成新的设备ID"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                        <span
                          id="cache-cleanable"
                          class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-600"
                          >检查中</span
                        >
                      </div>
                      <div class="text-xs text-gray-700">
                        状态:
                        <span
                          id="device-cache-status"
                          class="px-2 py-1 text-xs rounded bg-gray-100 text-gray-800"
                          title="缓存状态详情：已缓存表示设备ID已保存到本地文件，无缓存表示需要重新生成"
                          >检查中</span
                        >
                      </div>
                    </div>
                  </div>

                  <p class="text-xs text-slate-400 mt-2">
                    💡 清理成功后此ID会发生变化
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 设备信息 -->
          <div class="card rounded-2xl p-6 shadow-sm">
            <div class="flex items-center gap-3 mb-6">
              <div
                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                  />
                </svg>
              </div>
              <div class="flex-1">
                <h3 class="font-medium text-slate-800">设备信息</h3>
                <p class="text-sm text-slate-500">硬件详情</p>
              </div>
              <!-- 信息模式切换按钮 -->
              <button
                id="info-mode-toggle"
                onclick="toggleInfoMode()"
                class="px-3 py-1 text-xs bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-full transition-colors"
                data-tooltip="切换显示模式：详细信息 ⇄ 基础信息"
              >
                简洁
              </button>
            </div>

            <div id="device-info" class="space-y-3 text-sm">
              <!-- 基础信息 -->
              <div class="flex justify-between">
                <span class="text-slate-600">操作系统</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">处理器</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">总内存</span>
                <span class="font-medium text-slate-800">-</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-600">可用内存</span>
                <span class="font-medium text-slate-800">-</span>
              </div>

              <!-- 扩展信息 -->
              <div
                id="extended-info"
                class="border-t border-slate-200 pt-3 mt-4"
              >
                <div class="flex justify-between">
                  <span class="text-slate-600">运行时间</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-600">网络状态</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-600">当前用户</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-600">Node版本</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
              </div>

              <!-- 进程信息 -->
              <div
                id="process-info"
                class="border-t border-slate-200 pt-3 mt-4"
              >
                <h4 class="font-medium text-slate-700 mb-2">进程信息</h4>
                <div class="flex justify-between">
                  <span class="text-slate-600">进程ID</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-600">应用内存</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-slate-600">应用CPU</span>
                  <span class="font-medium text-slate-800">-</span>
                </div>
              </div>

              <!-- Cursor IDE 遥测标识符 -->
              <div
                class="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-200"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center gap-2">
                    <svg
                      class="w-4 h-4 text-purple-700"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                      />
                    </svg>
                    <h4
                      class="text-sm font-semibold text-purple-700"
                      id="ide-telemetry-title"
                    >
                      Cursor IDE 遥测标识符
                    </h4>
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      id="ide-telemetry-tooltip"
                      data-tooltip="📁 数据来源
直接从 Cursor IDE 配置文件读取

📍 存储位置
C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\storage.json

🔑 包含的真实ID
• devDeviceId - Cursor主设备标识符
• machineId - 机器唯一标识符
• macMachineId - MAC地址相关机器ID
• sessionId - 会话标识符
• sqmId - 软件质量监控ID

🧹 清理效果
重写 storage.json 文件，生成全新的遥测标识符
让 Cursor IDE 认为是新设备"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <span
                    id="cursor-telemetry-cleanable"
                    class="px-2 py-1 text-xs rounded bg-purple-100 text-purple-800"
                    >可清理</span
                  >
                </div>

                <!-- 主设备ID -->
                <div class="mb-2">
                  <div class="flex items-center gap-1 mb-1">
                    <span class="text-xs font-medium text-purple-600"
                      >主设备ID (devDeviceId)</span
                    >
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      data-tooltip="🔑 telemetry.devDeviceId
Cursor IDE 的主要设备标识符

🎯 用途
用户识别和扩展授权验证

📋 格式
UUID格式的唯一标识符

🧹 清理后
生成新的UUID，扩展将认为是新设备"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div
                    class="font-mono text-xs text-purple-800 bg-white border border-purple-200 px-2 py-1 rounded break-all cursor-pointer hover:bg-purple-50"
                    title="点击复制完整设备ID"
                  >
                    <span id="cursor-dev-device-id">获取中...</span>
                  </div>
                </div>

                <!-- 机器ID -->
                <div class="mb-2">
                  <div class="flex items-center gap-1 mb-1">
                    <span class="text-xs font-medium text-purple-600"
                      >机器ID (machineId)</span
                    >
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      data-tooltip="🖥️ telemetry.machineId
机器唯一标识符

🎯 用途
硬件级别的设备识别

📋 格式
UUID格式

🧹 清理后
生成新的机器ID，改变硬件指纹"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div
                    class="font-mono text-xs text-purple-800 bg-white border border-purple-200 px-2 py-1 rounded break-all cursor-pointer hover:bg-purple-50"
                    title="点击复制完整机器ID"
                  >
                    <span id="cursor-machine-id">获取中...</span>
                  </div>
                </div>

                <!-- MAC机器ID -->
                <div class="mb-2">
                  <div class="flex items-center gap-1 mb-1">
                    <span class="text-xs font-medium text-purple-600"
                      >MAC机器ID (macMachineId)</span
                    >
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      data-tooltip="🌐 telemetry.macMachineId
MAC地址相关机器ID

🎯 用途
基于网络接口的设备识别

📋 格式
UUID格式

🧹 清理后
生成新的MAC机器ID
改变网络层面的设备标识"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div
                    class="font-mono text-xs text-purple-800 bg-white border border-purple-200 px-2 py-1 rounded break-all cursor-pointer hover:bg-purple-50"
                    title="点击复制完整MAC机器ID"
                  >
                    <span id="cursor-mac-machine-id">获取中...</span>
                  </div>
                </div>

                <!-- 会话ID -->
                <div class="mb-2">
                  <div class="flex items-center gap-1 mb-1">
                    <span class="text-xs font-medium text-purple-600"
                      >会话ID (sessionId)</span
                    >
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      data-tooltip="📱 telemetry.sessionId
会话标识符

🎯 用途
跟踪用户会话和活动状态

📋 格式
UUID格式

🧹 清理后
生成新的会话ID，重置会话状态"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div
                    class="font-mono text-xs text-purple-800 bg-white border border-purple-200 px-2 py-1 rounded break-all cursor-pointer hover:bg-purple-50"
                    title="点击复制完整会话ID"
                  >
                    <span id="cursor-session-id">获取中...</span>
                  </div>
                </div>

                <!-- SQM ID -->
                <div class="mb-2">
                  <div class="flex items-center gap-1 mb-1">
                    <span class="text-xs font-medium text-purple-600"
                      >SQM ID (sqmId)</span
                    >
                    <svg
                      class="w-3 h-3 text-purple-400 cursor-help"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      data-tooltip="📊 telemetry.sqmId
软件质量监控ID

🎯 用途
软件质量数据收集和分析

📋 格式
GUID格式（大括号包围）

🧹 清理后
生成新的SQM ID
重置质量监控数据"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div
                    class="font-mono text-xs text-purple-800 bg-white border border-purple-200 px-2 py-1 rounded break-all cursor-pointer hover:bg-purple-50"
                    title="点击复制完整SQM ID"
                  >
                    <span id="cursor-sqm-id">获取中...</span>
                  </div>
                </div>

                <div
                  class="text-xs text-purple-600 mt-2 flex items-center gap-1"
                >
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  这些ID用于Cursor IDE的用户识别和遥测数据收集
                </div>
              </div>
            </div>

            <div class="flex gap-2 mt-4">
              <button
                onclick="refreshAllClientData()"
                class="flex-1 px-4 py-2 bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-600 transition-colors"
                title="刷新整个客户端数据"
              >
                刷新全部数据
              </button>
              <button
                onclick="exportSystemInfo()"
                class="px-4 py-2 bg-slate-500 text-white font-medium rounded-lg hover:bg-slate-600 transition-colors"
              >
                导出
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公告历史记录弹窗 -->
    <div
      id="announcement-history-modal"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 opacity-0 hidden transition-all duration-300"
      onclick="closeAnnouncementHistory(event)"
    >
      <div
        class="bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden scale-95 transition-all duration-300"
        onclick="event.stopPropagation()"
      >
        <!-- 弹窗头部 -->
        <div
          class="flex items-center justify-between p-6 border-b border-slate-200/50"
        >
          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
                />
                <path
                  d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
                />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-slate-800">公告历史记录</h3>
              <p class="text-sm text-slate-500">
                共 <span id="announcement-count">0</span> 条记录
              </p>
            </div>
          </div>
          <button
            onclick="closeAnnouncementHistory()"
            class="p-2 hover:bg-slate-100 rounded-lg transition-colors"
          >
            <svg
              class="w-5 h-5 text-slate-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>

        <!-- 弹窗内容 -->
        <div class="p-6 overflow-y-auto max-h-[60vh]">
          <div id="announcement-history-content">
            <!-- 历史记录内容将在这里动态加载 -->
          </div>
        </div>

        <!-- 弹窗底部 -->
        <div
          class="flex items-center justify-between p-6 border-t border-slate-200/50 bg-slate-50/50"
        >
          <div class="text-sm text-slate-500">
            历史记录会自动保存最近100条公告
          </div>
          <button
            onclick="clearAnnouncementHistory()"
            class="px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors text-sm font-medium"
          >
            清空历史
          </button>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="text-center mt-16 py-8 border-t border-slate-200">
      <p class="text-slate-400 text-sm">© 2025 Augment Team. 保留所有权利.</p>
    </footer>

    <script>
      // 主题切换功能
      function toggleTheme() {
        // 切换到丰富风格 (index-original.html)
        if (confirm("确定要切换到丰富风格吗？")) {
          window.location.href = "index-original.html";
        }
      }

      // 公告历史记录管理
      let announcementHistory = JSON.parse(
        localStorage.getItem("announcementHistory") || "[]"
      );

      // 显示公告历史记录
      function toggleAnnouncementHistory() {
        const modal = document.getElementById("announcement-history-modal");
        const content = document.getElementById("announcement-history-content");
        const count = document.getElementById("announcement-count");

        // 更新计数
        count.textContent = announcementHistory.length;

        // 生成历史记录HTML
        if (announcementHistory.length === 0) {
          content.innerHTML = `
            <div class="text-center py-12">
              <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                </svg>
              </div>
              <p class="text-slate-500">暂无公告历史记录</p>
            </div>
          `;
        } else {
          content.innerHTML = announcementHistory
            .map(
              (announcement, index) => `
            <div class="mb-4 p-4 bg-slate-50 rounded-xl border border-slate-200 hover:bg-slate-100 transition-colors">
              <div class="flex items-start justify-between mb-2">
                <div class="flex items-center gap-2">
                  <span class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    #${announcementHistory.length - index}
                  </span>
                  <span class="text-sm text-slate-500">${
                    announcement.timestamp
                  }</span>
                </div>
                <button
                  onclick="removeAnnouncement(${index})"
                  class="text-slate-400 hover:text-red-500 transition-colors"
                  title="删除此条记录"
                >
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </div>
              <div class="text-slate-700 leading-relaxed">${
                announcement.content
              }</div>
            </div>
          `
            )
            .join("");
        }

        // 显示弹窗
        modal.classList.remove("hidden");
        setTimeout(() => {
          modal.classList.remove("opacity-0");
          modal.querySelector(".bg-white\\/95").classList.remove("scale-95");
        }, 10);
      }

      // 关闭公告历史记录
      function closeAnnouncementHistory(event) {
        if (event && event.target !== event.currentTarget) return;

        const modal = document.getElementById("announcement-history-modal");
        modal.classList.add("opacity-0");
        modal.querySelector(".bg-white\\/95").classList.add("scale-95");

        setTimeout(() => {
          modal.classList.add("hidden");
        }, 300);
      }

      // 添加公告到历史记录
      function addAnnouncementToHistory(content) {
        const announcement = {
          content: content,
          timestamp: new Date().toLocaleString("zh-CN"),
          id: Date.now(),
        };

        announcementHistory.unshift(announcement);

        // 限制历史记录数量为100条
        if (announcementHistory.length > 100) {
          announcementHistory = announcementHistory.slice(0, 100);
        }

        // 保存到本地存储
        localStorage.setItem(
          "announcementHistory",
          JSON.stringify(announcementHistory)
        );

        // 更新徽章
        updateAnnouncementBadge();
      }

      // 删除单条公告记录
      function removeAnnouncement(index) {
        if (confirm("确定要删除这条公告记录吗？")) {
          announcementHistory.splice(index, 1);
          localStorage.setItem(
            "announcementHistory",
            JSON.stringify(announcementHistory)
          );
          toggleAnnouncementHistory(); // 刷新显示
          updateAnnouncementBadge();
        }
      }

      // 清空公告历史
      function clearAnnouncementHistory() {
        if (confirm("确定要清空所有公告历史记录吗？此操作不可恢复。")) {
          announcementHistory = [];
          localStorage.setItem(
            "announcementHistory",
            JSON.stringify(announcementHistory)
          );
          toggleAnnouncementHistory(); // 刷新显示
          updateAnnouncementBadge();
        }
      }

      // 更新公告徽章
      function updateAnnouncementBadge() {
        const badge = document.getElementById("announcement-badge");
        if (announcementHistory.length > 0) {
          badge.textContent = announcementHistory.length;
          badge.classList.remove("hidden");
        } else {
          badge.classList.add("hidden");
        }
      }

      // 页面加载完成后的初始化
      document.addEventListener("DOMContentLoaded", function () {
        // 更新主题切换按钮的提示文本
        const themeBtn = document.getElementById("theme-toggle-btn");
        if (themeBtn) {
          themeBtn.title = "切换到丰富风格";
        }

        // 初始化公告徽章
        updateAnnouncementBadge();

        // 监听来自renderer.js的公告
        window.addEventListener("new-announcement", (event) => {
          addAnnouncementToHistory(event.detail.content);
        });

        // 删除重复的refreshGuardianStatus函数定义，使用renderer.js中的版本

        window.updateGuardianStatusPanel = function (status) {
          // 更新防护模式显示（详细面板）
          const modeElement = document.getElementById("guardian-panel-mode");
          if (modeElement) {
            if (status.isRunning) {
              modeElement.textContent = "防护模式：独立服务（持久防护）";
              modeElement.className = "text-sm text-emerald-600 font-medium";
            } else {
              modeElement.textContent = "防护模式：未启动";
              modeElement.className = "text-sm text-slate-500";
            }
          }

          // 更新工具页面的防护状态
          const guardianStatus = document.getElementById("guardian-status");
          const persistentInfo = document.getElementById(
            "guardian-persistent-info"
          );

          if (guardianStatus) {
            if (status.isGuarding) {
              guardianStatus.textContent =
                status.mode === "standalone"
                  ? "独立服务运行中"
                  : "内置进程运行中";
              guardianStatus.className =
                "px-2 py-1 text-xs rounded-full bg-emerald-100 text-emerald-600";

              // 显示持续防护说明（仅独立服务模式）
              if (persistentInfo) {
                if (status.mode === "standalone") {
                  persistentInfo.style.display = "block";
                } else {
                  persistentInfo.style.display = "none";
                }
              }
            } else {
              guardianStatus.textContent = "未启动";
              guardianStatus.className =
                "px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600";

              // 隐藏持续防护说明
              if (persistentInfo) {
                persistentInfo.style.display = "none";
              }
            }
          }

          // 更新统计数据（详细面板）
          const interceptCount = document.getElementById(
            "panel-intercept-count"
          );
          const backupRemoved = document.getElementById("panel-backup-removed");
          const protectionRestored = document.getElementById(
            "panel-protection-restored"
          );

          if (interceptCount)
            interceptCount.textContent = status.stats?.interceptCount || 0;
          if (backupRemoved)
            backupRemoved.textContent = status.stats?.backupRemoved || 0;
          if (protectionRestored)
            protectionRestored.textContent =
              status.stats?.protectionRestored || 0;

          // 更新工具页面的统计数据
          const toolsInterceptCount =
            document.getElementById("intercept-count");
          const toolsBackupRemoved = document.getElementById("backup-removed");
          const toolsProtectionRestored = document.getElementById(
            "protection-restored"
          );

          // 统一使用renderer.js中的数据结构
          let stats = {
            interceptedAttempts: 0,
            backupFilesRemoved: 0,
            protectionRestored: 0,
          };

          if (
            status.standalone &&
            status.standalone.isRunning &&
            status.standalone.recentLogs
          ) {
            // 从独立服务日志解析统计
            stats = parseStatsFromLogs(status.standalone.recentLogs);
          } else if (status.inProcess && status.inProcess.stats) {
            // 从内置进程直接获取统计
            stats = {
              interceptedAttempts:
                status.inProcess.stats.interceptedAttempts || 0,
              backupFilesRemoved:
                status.inProcess.stats.backupFilesRemoved || 0,
              protectionRestored:
                status.inProcess.stats.protectionRestored || 0,
            };
          }

          if (toolsInterceptCount)
            toolsInterceptCount.textContent = stats.interceptedAttempts;
          if (toolsBackupRemoved)
            toolsBackupRemoved.textContent = stats.backupFilesRemoved;
          if (toolsProtectionRestored)
            toolsProtectionRestored.textContent = stats.protectionRestored;

          // 更新最近拦截记录
          updateRecentIntercepts(status.recentIntercepts || []);
        };

        // 从日志解析统计信息（与renderer.js保持一致）
        function parseStatsFromLogs(logs) {
          const stats = {
            interceptedAttempts: 0,
            backupFilesRemoved: 0,
            protectionRestored: 0,
          };

          if (!logs || !Array.isArray(logs)) {
            return stats;
          }

          logs.forEach((logEntry) => {
            // 处理不同格式的日志条目
            let logText = "";
            if (typeof logEntry === "string") {
              logText = logEntry;
            } else if (logEntry && typeof logEntry === "object") {
              // 处理对象格式的日志
              logText =
                logEntry.message ||
                logEntry.text ||
                logEntry.content ||
                JSON.stringify(logEntry);
            }

            // 拦截相关关键词（包括保护恢复事件）
            if (
              logText.includes("拦截") ||
              logText.includes("检测到") ||
              logText.includes("阻止") ||
              logText.includes("修改被拦截") ||
              logText.includes("IDE尝试") ||
              logText.includes("已拦截") ||
              logText.includes("保护恢复事件") ||
              logText.includes("设备ID已恢复") ||
              logText.includes("设备ID被篡改")
            ) {
              stats.interceptedAttempts++;
            }

            // 删除备份相关关键词
            if (
              logText.includes("删除备份") ||
              logText.includes("已删除") ||
              logText.includes("备份文件") ||
              logText.includes("清理备份") ||
              logText.includes("实时删除备份")
            ) {
              stats.backupFilesRemoved++;
            }

            // 恢复保护相关关键词
            if (
              logText.includes("恢复") ||
              logText.includes("已恢复") ||
              logText.includes("保护恢复") ||
              logText.includes("重新保护") ||
              logText.includes("设备ID已恢复")
            ) {
              stats.protectionRestored++;
            }
          });

          return stats;
        }

        window.updateRecentIntercepts = function (intercepts) {
          const container = document.getElementById("panel-recent-intercepts");
          if (!container) return;

          if (intercepts.length === 0) {
            container.innerHTML = `
              <div class="text-center text-slate-400 py-4">
                <svg class="w-8 h-8 mx-auto mb-2 text-slate-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"/>
                </svg>
                <div class="text-sm text-slate-400">暂无拦截记录</div>
              </div>
            `;
            return;
          }

          const interceptsHtml = intercepts
            .slice(0, 5)
            .map((intercept) => {
              const iconMap = {
                ide_operation: "🚨",
                backup_removal: "🗑️",
                protection_restore: "🔒",
              };
              const icon = iconMap[intercept.type] || "🛡️";
              const time = new Date(intercept.timestamp).toLocaleTimeString(
                "zh-CN",
                {
                  hour: "2-digit",
                  minute: "2-digit",
                }
              );

              return `
              <div class="flex items-center justify-between py-2 border-b border-slate-200 last:border-b-0">
                <div class="flex items-center gap-2">
                  <span class="text-sm">${icon}</span>
                  <span class="text-sm text-slate-700">${intercept.description}</span>
                </div>
                <span class="text-xs text-slate-500">${time}</span>
              </div>
            `;
            })
            .join("");

          container.innerHTML = interceptsHtml;
        };

        // 防护服务相关函数由renderer.js提供

        window.viewGuardianLogs = function () {
          // 获取查看日志按钮并设置loading状态
          const logBtn = document.querySelector(
            'button[onclick="viewGuardianLogs()"]'
          );
          const originalText = logBtn ? logBtn.innerHTML : "查看日志";

          if (logBtn) {
            logBtn.innerHTML = "📄 加载中...";
            logBtn.disabled = true;
          }

          // 直接调用IPC获取日志
          if (typeof require !== "undefined") {
            const { ipcRenderer } = require("electron");
            ipcRenderer
              .invoke("get-enhanced-guardian-status")
              .then((status) => {
                let logs = [];
                let logSource = "无日志";

                if (status.standalone && status.standalone.recentLogs) {
                  logs = status.standalone.recentLogs;
                  logSource = "独立服务日志";
                } else if (status.inProcess && status.inProcess.recentLogs) {
                  logs = status.inProcess.recentLogs;
                  logSource = "内置进程日志";
                }

                if (logs.length > 0) {
                  const logContent = logs
                    .map(
                      (log) =>
                        `<div class="py-1 border-b border-slate-200 text-sm">${log}</div>`
                    )
                    .join("");

                  // 使用showAlert显示日志
                  if (typeof showAlert === "function") {
                    showAlert(
                      `
                      <div class="text-left">
                        <h4 class="font-medium mb-2">${logSource}</h4>
                        <div class="max-h-64 overflow-y-auto bg-slate-50 p-3 rounded border">
                          ${logContent}
                        </div>
                      </div>
                    `,
                      "info"
                    );
                  } else {
                    showNotification(
                      `${logSource}: ${logs.join(", ")}`,
                      "info"
                    );
                  }
                } else {
                  showNotification("暂无防护日志", "info");
                }
              })
              .catch((err) => {
                console.error("获取防护日志失败:", err);
                showNotification("获取防护日志失败", "error");
              })
              .finally(() => {
                // 恢复按钮状态
                if (logBtn) {
                  logBtn.innerHTML = originalText;
                  logBtn.disabled = false;
                }
              });
          } else {
            // 恢复按钮状态
            if (logBtn) {
              logBtn.innerHTML = originalText;
              logBtn.disabled = false;
            }
            showNotification("无法获取防护日志", "error");
          }
        };

        // 通知函数
        window.showNotification = function (message, type = "info") {
          // 使用renderer.js中的showAlert函数
          if (typeof showAlert === "function") {
            showAlert(message, type);
          } else {
            // 备用方案
            alert(message);
          }
        };
      });
    </script>

    <!-- 确保函数在全局作用域中可用 -->
    <script>
      // 调试：检查renderer.js函数是否可用
      console.log("🔍 HTML脚本执行时的函数检查:", {
        updateGuardianStatusDisplay: typeof window.updateGuardianStatusDisplay,
        refreshGuardianStatus: typeof window.refreshGuardianStatus,
        startGuardianService: typeof window.startGuardianService,
        stopAllNodeProcesses: typeof window.stopAllNodeProcesses,
      });

      // 等待renderer.js加载完成后再初始化
      document.addEventListener("DOMContentLoaded", function () {
        // 确保函数可用
        if (typeof switchTab === "undefined") {
          window.switchTab = function (tabName) {
            console.log("switchTab fallback:", tabName);
          };
        }

        if (typeof testServerConnection === "undefined") {
          window.testServerConnection = function () {
            console.log("testServerConnection fallback");
          };
        }

        if (typeof refreshAllClientData === "undefined") {
          window.refreshAllClientData = function () {
            console.log("refreshAllClientData fallback");
          };
        }

        console.log("✅ 页面初始化完成");
      });
    </script>
  </body>
</html>
