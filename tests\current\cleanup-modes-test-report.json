{"timestamp": "2025-07-06T16:25:51.238Z", "results": {"intelligent": {"success": true, "actions": ["🔍 检查增强防护状态...", "✅ 增强防护未运行，可以直接进行清理", "🧠 使用智能清理模式 - 精准清理设备身份", "🧠 开始智能清理 - 精准清理设备身份数据", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🛡️ 已保护IDE设置: settings.json", "🛡️ 已保护IDE设置: keybindings.json", "🛡️ 已保护代码片段: 1 个文件", "✅ IDE设置保护完成，共保护 3 项配置", "ℹ️ 未发现需要保护的工作区配置", "🧪 [DRY RUN] cleanDeviceIdentityOnly() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentDeviceIdentity() - 执行模拟操作", "🧪 [DRY RUN] regenerateDeviceFingerprint() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复IDE设置: settings.json", "🔄 已恢复IDE设置: keybindings.json", "🔄 已恢复代码片段: 1 个文件", "✅ IDE设置恢复完成", "✅ 智能清理完成 - 设备身份已重置，所有配置已保留", "🛡️ 保护范围: MCP配置 + IDE设置 + 工作区配置 + 登录状态", "🎯 效果: 扩展识别为新用户，但保留所有个人配置", "⚠️ 重要提醒: 智能模式仅更新设备身份，不清理任何IDE配置文件"], "errors": [], "options": {"intelligentMode": true, "preserveActivation": true, "deepClean": false, "cleanCursorExtension": false, "autoRestartCursor": false, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": true, "resetCursorCompletely": false, "resetVSCodeCompletely": false, "aggressiveMode": false, "multiRoundClean": false, "extendedMonitoring": false, "usePowerShellAssist": false, "cleanCursor": false, "cleanVSCode": false}}, "standard": {"success": true, "actions": ["🔍 检查增强防护状态...", "✅ 增强防护未运行，可以直接进行清理", "🔧 使用标准清理模式 - 深度清理保留核心配置", "🔧 开始标准清理 - 深度清理保留核心配置", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🔄 使用标准清理模式", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🧪 [DRY RUN] cleanActivationData() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentStorage() - 执行模拟操作", "🧪 [DRY RUN] cleanStateDatabase() - 执行模拟操作", "🧪 [DRY RUN] cleanWindowsRegistry() - 执行模拟操作", "🧪 [DRY RUN] cleanTempFiles() - 执行模拟操作", "🧪 [DRY RUN] cleanBrowserData() - 执行模拟操作", "🧪 [DRY RUN] cleanCursorExtensionData() - 执行模拟操作", "🧪 [DRY RUN] regenerateDeviceFingerprint() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "✅ 标准清理完成 - MCP配置已保护", "✅ 标准清理完成 - 深度清理已完成，MCP配置已保护"], "errors": [], "options": {"standardMode": true, "preserveActivation": true, "deepClean": true, "cleanCursorExtension": true, "autoRestartCursor": true, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": true, "resetCursorCompletely": false, "resetVSCodeCompletely": false, "aggressiveMode": true, "multiRoundClean": true, "extendedMonitoring": true, "usePowerShellAssist": true, "cleanCursor": true, "cleanVSCode": false}}, "complete": {"success": true, "actions": ["🔍 检查增强防护状态...", "✅ 增强防护未运行，可以直接进行清理", "💥 使用完全清理模式 - 彻底重置仅保护MCP", "💥 开始完全清理 - 彻底重置仅保护MCP配置", "🛡️ 已保护MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "🧪 [DRY RUN] forceCloseCursorIDE() - 执行模拟操作", "🧪 [DRY RUN] cleanActivationData() - 执行模拟操作", "🧪 [DRY RUN] cleanAugmentStorage() - 执行模拟操作", "🧪 [DRY RUN] cleanStateDatabase() - 执行模拟操作", "🧪 [DRY RUN] cleanWindowsRegistry() - 执行模拟操作", "🧪 [DRY RUN] cleanTempFiles() - 执行模拟操作", "🧪 [DRY RUN] cleanBrowserData() - 执行模拟操作", "🧪 [DRY RUN] performCompleteCursorReset() - 执行模拟操作", "🧪 [DRY RUN] performCompleteVSCodeReset() - 执行模拟操作", "🔄 已恢复MCP配置: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\augment.vscode-augment\\augment-global-state\\mcpServers.json", "✅ 完全清理完成 - IDE已彻底重置，MCP配置已恢复"], "errors": [], "options": {"completeMode": true, "preserveActivation": true, "deepClean": true, "cleanCursorExtension": true, "autoRestartCursor": true, "skipBackup": true, "enableEnhancedGuardian": true, "skipCursorLogin": false, "resetCursorCompletely": true, "resetVSCodeCompletely": true, "aggressiveMode": true, "multiRoundClean": true, "extendedMonitoring": true, "usePowerShellAssist": true, "cleanCursor": true, "cleanVSCode": true}}}, "summary": {"intelligent_steps": 22, "standard_steps": 19, "complete_steps": 16}}